name = Views
description = Create customized lists and queries from your database.
package = Views
core = 7.x

; Always available CSS
stylesheets[all][] = css/views.css

; THe Views system has only one dependency, everything else is built into core.
dependencies[] = ctools

; Handlers
files[] = handlers/views_handler_area.inc
files[] = handlers/views_handler_area_messages.inc
files[] = handlers/views_handler_area_result.inc
files[] = handlers/views_handler_area_text.inc
files[] = handlers/views_handler_area_text_custom.inc
files[] = handlers/views_handler_area_view.inc
files[] = handlers/views_handler_argument.inc
files[] = handlers/views_handler_argument_date.inc
files[] = handlers/views_handler_argument_formula.inc
files[] = handlers/views_handler_argument_many_to_one.inc
files[] = handlers/views_handler_argument_null.inc
files[] = handlers/views_handler_argument_numeric.inc
files[] = handlers/views_handler_argument_string.inc
files[] = handlers/views_handler_argument_group_by_numeric.inc
files[] = handlers/views_handler_field.inc
files[] = handlers/views_handler_field_counter.inc
files[] = handlers/views_handler_field_boolean.inc
files[] = handlers/views_handler_field_contextual_links.inc
files[] = handlers/views_handler_field_ctools_dropdown.inc
files[] = handlers/views_handler_field_custom.inc
files[] = handlers/views_handler_field_date.inc
files[] = handlers/views_handler_field_entity.inc
files[] = handlers/views_handler_field_links.inc
files[] = handlers/views_handler_field_markup.inc
files[] = handlers/views_handler_field_math.inc
files[] = handlers/views_handler_field_numeric.inc
files[] = handlers/views_handler_field_prerender_list.inc
files[] = handlers/views_handler_field_time_interval.inc
files[] = handlers/views_handler_field_serialized.inc
files[] = handlers/views_handler_field_machine_name.inc
files[] = handlers/views_handler_field_url.inc
files[] = handlers/views_handler_filter.inc
files[] = handlers/views_handler_filter_boolean_operator.inc
files[] = handlers/views_handler_filter_boolean_operator_string.inc
files[] = handlers/views_handler_filter_combine.inc
files[] = handlers/views_handler_filter_date.inc
files[] = handlers/views_handler_filter_equality.inc
files[] = handlers/views_handler_filter_entity_bundle.inc
files[] = handlers/views_handler_filter_group_by_numeric.inc
files[] = handlers/views_handler_filter_in_operator.inc
files[] = handlers/views_handler_filter_many_to_one.inc
files[] = handlers/views_handler_filter_numeric.inc
files[] = handlers/views_handler_filter_string.inc
files[] = handlers/views_handler_filter_fields_compare.inc
files[] = handlers/views_handler_relationship.inc
files[] = handlers/views_handler_relationship_groupwise_max.inc
files[] = handlers/views_handler_sort.inc
files[] = handlers/views_handler_sort_date.inc
files[] = handlers/views_handler_sort_formula.inc
files[] = handlers/views_handler_sort_group_by_numeric.inc
files[] = handlers/views_handler_sort_menu_hierarchy.inc
files[] = handlers/views_handler_sort_random.inc
; Includes
files[] = includes/base.inc
files[] = includes/handlers.inc
files[] = includes/plugins.inc
files[] = includes/view.inc
; Modules
files[] = modules/aggregator/views_handler_argument_aggregator_fid.inc
files[] = modules/aggregator/views_handler_argument_aggregator_iid.inc
files[] = modules/aggregator/views_handler_argument_aggregator_category_cid.inc
files[] = modules/aggregator/views_handler_field_aggregator_title_link.inc
files[] = modules/aggregator/views_handler_field_aggregator_category.inc
files[] = modules/aggregator/views_handler_field_aggregator_item_description.inc
files[] = modules/aggregator/views_handler_field_aggregator_xss.inc
files[] = modules/aggregator/views_handler_filter_aggregator_category_cid.inc
files[] = modules/aggregator/views_plugin_row_aggregator_rss.inc
files[] = modules/book/views_plugin_argument_default_book_root.inc
files[] = modules/comment/views_handler_argument_comment_user_uid.inc
files[] = modules/comment/views_handler_field_comment.inc
files[] = modules/comment/views_handler_field_comment_depth.inc
files[] = modules/comment/views_handler_field_comment_link.inc
files[] = modules/comment/views_handler_field_comment_link_approve.inc
files[] = modules/comment/views_handler_field_comment_link_delete.inc
files[] = modules/comment/views_handler_field_comment_link_edit.inc
files[] = modules/comment/views_handler_field_comment_link_reply.inc
files[] = modules/comment/views_handler_field_comment_node_link.inc
files[] = modules/comment/views_handler_field_comment_username.inc
files[] = modules/comment/views_handler_field_ncs_last_comment_name.inc
files[] = modules/comment/views_handler_field_ncs_last_updated.inc
files[] = modules/comment/views_handler_field_node_comment.inc
files[] = modules/comment/views_handler_field_node_new_comments.inc
files[] = modules/comment/views_handler_field_last_comment_timestamp.inc
files[] = modules/comment/views_handler_filter_comment_user_uid.inc
files[] = modules/comment/views_handler_filter_ncs_last_updated.inc
files[] = modules/comment/views_handler_filter_node_comment.inc
files[] = modules/comment/views_handler_sort_comment_thread.inc
files[] = modules/comment/views_handler_sort_ncs_last_comment_name.inc
files[] = modules/comment/views_handler_sort_ncs_last_updated.inc
files[] = modules/comment/views_plugin_row_comment_rss.inc
files[] = modules/comment/views_plugin_row_comment_view.inc
files[] = modules/contact/views_handler_field_contact_link.inc
files[] = modules/field/views_handler_field_field.inc
files[] = modules/field/views_handler_relationship_entity_reverse.inc
files[] = modules/field/views_handler_argument_field_list.inc
files[] = modules/field/views_handler_filter_field_list_boolean.inc
files[] = modules/field/views_handler_argument_field_list_string.inc
files[] = modules/field/views_handler_filter_field_list.inc
files[] = modules/filter/views_handler_field_filter_format_name.inc
files[] = modules/locale/views_handler_field_node_language.inc
files[] = modules/locale/views_handler_filter_node_language.inc
files[] = modules/locale/views_handler_argument_locale_group.inc
files[] = modules/locale/views_handler_argument_locale_language.inc
files[] = modules/locale/views_handler_field_locale_group.inc
files[] = modules/locale/views_handler_field_locale_language.inc
files[] = modules/locale/views_handler_field_locale_link_edit.inc
files[] = modules/locale/views_handler_filter_locale_group.inc
files[] = modules/locale/views_handler_filter_locale_language.inc
files[] = modules/locale/views_handler_filter_locale_version.inc
files[] = modules/locale/views_handler_sort_node_language.inc
files[] = modules/node/views_handler_argument_dates_various.inc
files[] = modules/node/views_handler_argument_node_language.inc
files[] = modules/node/views_handler_argument_node_nid.inc
files[] = modules/node/views_handler_argument_node_type.inc
files[] = modules/node/views_handler_argument_node_vid.inc
files[] = modules/node/views_handler_argument_node_uid_revision.inc
files[] = modules/node/views_handler_field_history_user_timestamp.inc
files[] = modules/node/views_handler_field_node.inc
files[] = modules/node/views_handler_field_node_link.inc
files[] = modules/node/views_handler_field_node_link_delete.inc
files[] = modules/node/views_handler_field_node_link_edit.inc
files[] = modules/node/views_handler_field_node_revision.inc
files[] = modules/node/views_handler_field_node_revision_link.inc
files[] = modules/node/views_handler_field_node_revision_link_delete.inc
files[] = modules/node/views_handler_field_node_revision_link_revert.inc
files[] = modules/node/views_handler_field_node_path.inc
files[] = modules/node/views_handler_field_node_type.inc
files[] = modules/node/views_handler_field_node_version_count.inc
files[] = modules/node/views_handler_filter_history_user_timestamp.inc
files[] = modules/node/views_handler_filter_node_access.inc
files[] = modules/node/views_handler_filter_node_status.inc
files[] = modules/node/views_handler_filter_node_type.inc
files[] = modules/node/views_handler_filter_node_uid_revision.inc
files[] = modules/node/views_handler_filter_node_version_count.inc
files[] = modules/node/views_handler_sort_node_version_count.inc
files[] = modules/node/views_plugin_argument_default_node.inc
files[] = modules/node/views_plugin_argument_validate_node.inc
files[] = modules/node/views_plugin_row_node_rss.inc
files[] = modules/node/views_plugin_row_node_view.inc
files[] = modules/profile/views_handler_field_profile_date.inc
files[] = modules/profile/views_handler_field_profile_list.inc
files[] = modules/profile/views_handler_filter_profile_selection.inc
files[] = modules/search/views_handler_argument_search.inc
files[] = modules/search/views_handler_field_search_score.inc
files[] = modules/search/views_handler_filter_search.inc
files[] = modules/search/views_handler_sort_search_score.inc
files[] = modules/search/views_plugin_row_search_view.inc
files[] = modules/statistics/views_handler_field_accesslog_path.inc
files[] = modules/statistics/views_handler_field_node_counter_timestamp.inc
files[] = modules/statistics/views_handler_field_statistics_numeric.inc
files[] = modules/system/views_handler_argument_file_fid.inc
files[] = modules/system/views_handler_field_file.inc
files[] = modules/system/views_handler_field_file_extension.inc
files[] = modules/system/views_handler_field_file_filemime.inc
files[] = modules/system/views_handler_field_file_uri.inc
files[] = modules/system/views_handler_field_file_status.inc
files[] = modules/system/views_handler_filter_file_status.inc
files[] = modules/taxonomy/views_handler_argument_taxonomy.inc
files[] = modules/taxonomy/views_handler_argument_term_node_tid.inc
files[] = modules/taxonomy/views_handler_argument_term_node_tid_depth.inc
files[] = modules/taxonomy/views_handler_argument_term_node_tid_depth_join.inc
files[] = modules/taxonomy/views_handler_argument_term_node_tid_depth_modifier.inc
files[] = modules/taxonomy/views_handler_argument_vocabulary_vid.inc
files[] = modules/taxonomy/views_handler_argument_vocabulary_machine_name.inc
files[] = modules/taxonomy/views_handler_field_taxonomy.inc
files[] = modules/taxonomy/views_handler_field_term_node_tid.inc
files[] = modules/taxonomy/views_handler_field_term_link_edit.inc
files[] = modules/taxonomy/views_handler_filter_term_node_tid.inc
files[] = modules/taxonomy/views_handler_filter_term_node_tid_depth.inc
files[] = modules/taxonomy/views_handler_filter_term_node_tid_depth_join.inc
files[] = modules/taxonomy/views_handler_filter_vocabulary_vid.inc
files[] = modules/taxonomy/views_handler_filter_vocabulary_machine_name.inc
files[] = modules/taxonomy/views_handler_relationship_node_term_data.inc
files[] = modules/taxonomy/views_plugin_argument_validate_taxonomy_term.inc
files[] = modules/taxonomy/views_plugin_argument_default_taxonomy_tid.inc
files[] = modules/tracker/views_handler_argument_tracker_comment_user_uid.inc
files[] = modules/tracker/views_handler_filter_tracker_comment_user_uid.inc
files[] = modules/tracker/views_handler_filter_tracker_boolean_operator.inc
files[] = modules/system/views_handler_filter_system_type.inc
files[] = modules/translation/views_handler_argument_node_tnid.inc
files[] = modules/translation/views_handler_field_node_link_translate.inc
files[] = modules/translation/views_handler_field_node_translation_link.inc
files[] = modules/translation/views_handler_filter_node_tnid.inc
files[] = modules/translation/views_handler_filter_node_tnid_child.inc
files[] = modules/translation/views_handler_relationship_translation.inc
files[] = modules/user/views_handler_argument_user_uid.inc
files[] = modules/user/views_handler_argument_users_roles_rid.inc
files[] = modules/user/views_handler_field_user.inc
files[] = modules/user/views_handler_field_user_language.inc
files[] = modules/user/views_handler_field_user_link.inc
files[] = modules/user/views_handler_field_user_link_cancel.inc
files[] = modules/user/views_handler_field_user_link_edit.inc
files[] = modules/user/views_handler_field_user_mail.inc
files[] = modules/user/views_handler_field_user_name.inc
files[] = modules/user/views_handler_field_user_permissions.inc
files[] = modules/user/views_handler_field_user_picture.inc
files[] = modules/user/views_handler_field_user_roles.inc
files[] = modules/user/views_handler_filter_user_current.inc
files[] = modules/user/views_handler_filter_user_name.inc
files[] = modules/user/views_handler_filter_user_permissions.inc
files[] = modules/user/views_handler_filter_user_roles.inc
files[] = modules/user/views_plugin_argument_default_current_user.inc
files[] = modules/user/views_plugin_argument_default_user.inc
files[] = modules/user/views_plugin_argument_validate_user.inc
files[] = modules/user/views_plugin_row_user_view.inc
; Plugins
files[] = plugins/views_plugin_access.inc
files[] = plugins/views_plugin_access_none.inc
files[] = plugins/views_plugin_access_perm.inc
files[] = plugins/views_plugin_access_role.inc
files[] = plugins/views_plugin_argument_default.inc
files[] = plugins/views_plugin_argument_default_php.inc
files[] = plugins/views_plugin_argument_default_fixed.inc
files[] = plugins/views_plugin_argument_default_raw.inc
files[] = plugins/views_plugin_argument_validate.inc
files[] = plugins/views_plugin_argument_validate_numeric.inc
files[] = plugins/views_plugin_argument_validate_php.inc
files[] = plugins/views_plugin_cache.inc
files[] = plugins/views_plugin_cache_none.inc
files[] = plugins/views_plugin_cache_time.inc
files[] = plugins/views_plugin_display.inc
files[] = plugins/views_plugin_display_attachment.inc
files[] = plugins/views_plugin_display_block.inc
files[] = plugins/views_plugin_display_default.inc
files[] = plugins/views_plugin_display_embed.inc
files[] = plugins/views_plugin_display_extender.inc
files[] = plugins/views_plugin_display_feed.inc
files[] = plugins/views_plugin_display_page.inc
files[] = plugins/views_plugin_exposed_form_basic.inc
files[] = plugins/views_plugin_exposed_form.inc
files[] = plugins/views_plugin_exposed_form_input_required.inc
files[] = plugins/views_plugin_localization_core.inc
files[] = plugins/views_plugin_localization.inc
files[] = plugins/views_plugin_localization_none.inc
files[] = plugins/views_plugin_pager.inc
files[] = plugins/views_plugin_pager_full.inc
files[] = plugins/views_plugin_pager_mini.inc
files[] = plugins/views_plugin_pager_none.inc
files[] = plugins/views_plugin_pager_some.inc
files[] = plugins/views_plugin_query.inc
files[] = plugins/views_plugin_query_default.inc
files[] = plugins/views_plugin_row.inc
files[] = plugins/views_plugin_row_fields.inc
files[] = plugins/views_plugin_row_rss_fields.inc
files[] = plugins/views_plugin_style.inc
files[] = plugins/views_plugin_style_default.inc
files[] = plugins/views_plugin_style_grid.inc
files[] = plugins/views_plugin_style_list.inc
files[] = plugins/views_plugin_style_jump_menu.inc
files[] = plugins/views_plugin_style_mapping.inc
files[] = plugins/views_plugin_style_rss.inc
files[] = plugins/views_plugin_style_summary.inc
files[] = plugins/views_plugin_style_summary_jump_menu.inc
files[] = plugins/views_plugin_style_summary_unformatted.inc
files[] = plugins/views_plugin_style_table.inc

; Tests
files[] = tests/handlers/views_handlers.test
files[] = tests/handlers/views_handler_area_text.test
files[] = tests/handlers/views_handler_argument_null.test
files[] = tests/handlers/views_handler_field.test
files[] = tests/handlers/views_handler_field_boolean.test
files[] = tests/handlers/views_handler_field_custom.test
files[] = tests/handlers/views_handler_field_counter.test
files[] = tests/handlers/views_handler_field_date.test
files[] = tests/handlers/views_handler_field_file_extension.test
files[] = tests/handlers/views_handler_field_file_size.test
files[] = tests/handlers/views_handler_field_math.test
files[] = tests/handlers/views_handler_field_url.test
files[] = tests/handlers/views_handler_field_xss.test
files[] = tests/handlers/views_handler_filter_combine.test
files[] = tests/handlers/views_handler_filter_date.test
files[] = tests/handlers/views_handler_filter_equality.test
files[] = tests/handlers/views_handler_filter_in_operator.test
files[] = tests/handlers/views_handler_filter_numeric.test
files[] = tests/handlers/views_handler_filter_string.test
files[] = tests/handlers/views_handler_manytoone.test
files[] = tests/handlers/views_handler_sort_random.test
files[] = tests/handlers/views_handler_sort_date.test
files[] = tests/handlers/views_handler_sort.test
files[] = tests/test_handlers/views_test_area_access.inc
files[] = tests/test_plugins/views_test_plugin_access_test_dynamic.inc
files[] = tests/test_plugins/views_test_plugin_access_test_static.inc
files[] = tests/test_plugins/views_test_plugin_style_test_mapping.inc
files[] = tests/plugins/views_plugin_display.test
files[] = tests/styles/views_plugin_style_jump_menu.test
files[] = tests/styles/views_plugin_style.test
files[] = tests/styles/views_plugin_style_base.test
files[] = tests/styles/views_plugin_style_mapping.test
files[] = tests/styles/views_plugin_style_unformatted.test
files[] = tests/views_access.test
files[] = tests/views_analyze.test
files[] = tests/views_basic.test
files[] = tests/views_ajax.test
files[] = tests/views_argument_default.test
files[] = tests/views_argument_validator.test
files[] = tests/views_exposed_form.test
files[] = tests/field/views_fieldapi.test
files[] = tests/views_glossary.test
files[] = tests/views_groupby.test
files[] = tests/views_handler_filter.test
files[] = tests/views_handlers.test
files[] = tests/views_module.test
files[] = tests/views_pager.test
files[] = tests/views_plugin_localization_test.inc
files[] = tests/views_translatable.test
files[] = tests/views_query.test
files[] = tests/views_upgrade.test
files[] = tests/views_test.views_default.inc
files[] = tests/comment/views_handler_argument_comment_user_uid.test
files[] = tests/comment/views_handler_filter_comment_user_uid.test
files[] = tests/node/views_node_revision_relations.test
files[] = tests/taxonomy/views_handler_relationship_node_term_data.test
files[] = tests/user/views_handler_field_user_name.test
files[] = tests/user/views_user_argument_default.test
files[] = tests/user/views_user_argument_validate.test
files[] = tests/user/views_user.test
files[] = tests/views_cache.test
files[] = tests/views_clone.test
files[] = tests/views_view.test
files[] = tests/views_ui.test

; Information added by Drupal.org packaging script on 2025-03-13
version = "7.x-3.30"
core = "7.x"
project = "views"
datestamp = "1741861463"
