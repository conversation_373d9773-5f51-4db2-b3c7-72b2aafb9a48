<?php

/**
 * @file
 * Contains install and update functions for Views.
 */

/**
 * Implements hook_install().
 */
function views_install() {
  if (Database::getConnection()->databaseType() == 'pgsql') {
    db_query('CREATE OR REPLACE FUNCTION first(anyelement, anyelement) RETURNS anyelement AS \'SELECT COALESCE($1, $2);\' LANGUAGE \'sql\';');
    db_query("DROP AGGREGATE IF EXISTS first(anyelement)");
    db_query("CREATE AGGREGATE first(sfunc = first, basetype = anyelement, stype = anyelement);");
  }
  db_query("UPDATE {system} SET weight = 10 WHERE name = 'views'");
}

/**
 * Implements hook_schema().
 */
function views_schema($caller_function = NULL) {
  // Generate the current version of the database schema from the sequence of
  // schema update functions. Uses a similar method to install.inc's
  // drupal_get_schema_versions() to establish the update sequence.
  //
  // To change the schema, add a new views_schema_N() function to match the
  // associated views_update_N().
  //
  // @param string $caller_function
  //   The name of the function that called us. Used internally, if requesting a
  //   specific schema version.
  static $get_current;
  static $schemas = array();

  // If called with no arguments, get the latest version of the schema.
  if (!isset($get_current)) {
    $get_current = $caller_function ? FALSE : TRUE;
  }

  // Generate a sorted list of available schema update functions.
  if ($get_current || empty($schemas)) {
    $get_current = FALSE;
    // Provide a worst-case scenario range.
    $start_schema = 6000;
    $end_schema = 7999;
    for ($i = $start_schema; $i <= $end_schema; $i++) {
      if (function_exists('views_schema_' . $i)) {
        $schemas[] = $i;
      }
    }
    if ($schemas) {
      sort($schemas, SORT_NUMERIC);

      // If a specific version was requested, drop any later updates from the
      // sequence.
      if ($caller_function) {
        do {
          $schema = array_pop($schemas);
        } while ($schemas && $caller_function != 'views_schema_' . $schema);
      }
    }
  }

  // Call views_schema_<n>, for the highest available <n>.
  if ($schema = array_pop($schemas)) {
    $function = 'views_schema_' . $schema;
    return $function();
  }

  return array();
}

/**
 * Views 2's initial schema.
 *
 * Called directly by views_update_6000() for updates from Drupal 5.
 *
 * Important: Do not edit this schema!
 *
 * Updates to the views schema must be provided as views_schema_6xxx()
 * functions, which views_schema() automatically sees and applies. See below for
 * examples.
 *
 * Please do document updates with comments in this function, however.
 */
function views_schema_6000() {
  $schema['views_view'] = array(
    'description' => 'Stores the general data for a view.',
    'export' => array(
      'identifier' => 'view',
      'bulk export' => TRUE,
      'primary key' => 'vid',
      'default hook' => 'views_default_views',
      'admin_title' => 'human_name',
      'admin_description' => 'description',
      'api' => array(
        'owner' => 'views',
        'api' => 'views_default',
        'minimum_version' => '2',
        'current_version' => '3.0',
      ),
      'object' => 'view',
      // The callback to load the displays.
      'subrecords callback' => 'views_load_display_records',
      // The variable that holds enabled/disabled status.
      'status' => 'views_defaults',
      // CRUD callbacks.
      'create callback' => 'views_new_view',
      'save callback' => 'views_save_view',
      'delete callback' => 'views_delete_view',
      'export callback' => 'views_export_view',
      'status callback' => 'views_export_status',
      'cache defaults' => TRUE,
      'default cache bin' => 'cache_views',
    ),
    'fields' => array(
      'vid' => array(
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'The view ID of the field, defined by the database.',
        'no export' => TRUE,
      ),
      'name' => array(
        'type' => 'varchar',
        'length' => '32',
        'default' => '',
        'not null' => TRUE,
        'description' => 'The unique name of the view. This is the primary field views are loaded from, and is used so that views may be internal and not necessarily in the database. May only be alphanumeric characters plus underscores.',
      ),
      'description' => array(
        'type' => 'varchar',
        'length' => '255',
        'default' => '',
        'description' => 'A description of the view for the admin interface.',
      ),
      'tag' => array(
        'type' => 'varchar',
        'length' => '255',
        'default' => '',
        'description' => 'A tag used to group/sort views in the admin interface',
      ),
      'view_php' => array(
        'type' => 'blob',
        'description' => 'A chunk of PHP code that can be used to provide modifications to the view prior to building.',
      ),
      'base_table' => array(
        'type' => 'varchar',
        'length' => '32',
    // Updated to '64' in views_schema_6005()
        'default' => '',
        'not null' => TRUE,
        'description' => 'What table this view is based on, such as node, user, comment, or term.',
      ),
      'is_cacheable' => array(
        'type' => 'int',
        'default' => 0,
        'size' => 'tiny',
        'description' => 'A boolean to indicate whether or not this view may have its query cached.',
      ),
    ),
    'primary key' => array('vid'),
    'unique key' => array('name' => array('name')),
  // Updated to 'unique keys' in views_schema_6003()
  );

  $schema['views_display'] = array(
    'description' => 'Stores information about each display attached to a view.',
    'fields' => array(
      'vid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The view this display is attached to.',
        'no export' => TRUE,
      ),
      'id' => array(
        'type' => 'varchar',
        'length' => '64',
        'default' => '',
        'not null' => TRUE,
        'description' => 'An identifier for this display; usually generated from the display_plugin, so should be something like page or page_1 or block_2, etc.',
      ),
      'display_title' => array(
        'type' => 'varchar',
        'length' => '64',
        'default' => '',
        'not null' => TRUE,
        'description' => 'The title of the display, viewable by the administrator.',
      ),
      'display_plugin' => array(
        'type' => 'varchar',
        'length' => '64',
        'default' => '',
        'not null' => TRUE,
        'description' => 'The type of the display. Usually page, block or embed, but is pluggable so may be other things.',
      ),
      'position' => array(
        'type' => 'int',
        'default' => 0,
        'description' => 'The order in which this display is loaded.',
      ),
      'display_options' => array(
        // Type corrected in update 6009.
        'type' => 'blob',
        'description' => 'A serialized array of options for this display; it contains options that are generally only pertinent to that display plugin type.',
        'serialize' => TRUE,
        'serialized default' => 'a:0:{}',
      ),
    ),
    // Added primary keys in views_schema_6008()
    'indexes' => array('vid' => array('vid', 'position')),
  );

  $schema['cache_views'] = drupal_get_schema_unprocessed('system', 'cache');

  $schema['views_object_cache'] = array(
    'description' => 'A special cache used to store objects that are being edited; it serves to save state in an ordinarily stateless environment.',
    'fields' => array(
      'sid' => array(
        'type' => 'varchar',
        'length' => '64',
        'description' => 'The session ID this cache object belongs to.',
      ),
      'name' => array(
        'type' => 'varchar',
        'length' => '32',
        'description' => 'The name of the view this cache is attached to.',
      ),
      'obj' => array(
        'type' => 'varchar',
        'length' => '32',
        'description' => 'The name of the object this cache is attached to; this essentially represents the owner so that several sub-systems can use this cache.',
      ),
      'updated' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The time this cache was created or updated.',
      ),
      'data' => array(
        'type' => 'blob',
    // Updated to 'text' (with size => 'big') in views_schema_6004()
        'description' => 'Serialized data being stored.',
        'serialize' => TRUE,
      ),
    ),
    'indexes' => array(
      'sid_obj_name' => array('sid', 'obj', 'name'),
      'updated' => array('updated'),
    ),
  );

  // $schema['cache_views_data'] added in views_schema_6006().
  return $schema;
}

/**
 * Update a site to Drupal 6! Contains a bit of special code to detect
 * if you've been running a beta version or something.
 */
function views_update_6000() {
  if (db_table_exists('views_view')) {
    return;
  }

  // This has the beneficial effect of wiping out any Views 1 cache at the
  // same time; not wiping that cache could easily cause problems with Views 2.
  if (db_table_exists('cache_views')) {
    db_drop_table('cache_views');
  }

  // This is mostly the same as drupal_install_schema, but it forces
  // views_schema_6000() rather than the default views_schema().
  // This is important for processing subsequent table updates.
  $schema = views_schema_6000();
  _drupal_schema_initialize($schema, 'views');

  foreach ($schema as $name => $table) {
    db_create_table($name, $table);
  }
}

/**
 * Remove '$' symbol in special blocks, as it is invalid for theming.
 */
function views_update_6001() {
  $result = db_query("SELECT * FROM {blocks} WHERE module = 'views' AND delta LIKE '\$exp%'");
  foreach ($result as $block) {
    $new = strtr($block->delta, '$', '-');
    update_sql("UPDATE {blocks} SET delta = '" . db_escape_string($new) . "' WHERE module = 'views' AND delta = '" . db_escape_string($block->delta) . "'");
  }
  update_sql("UPDATE {blocks} SET delta = CONCAT(delta, '-block_1') WHERE module = 'views'");
}

/**
 * NOTE: Update 6002 removed because it did not always work.
 * Update 6004 implements the change correctly.
 */

/**
 * Add missing unique key.
 */
function views_schema_6003() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_view']['unique keys'] = array('name' => array('name'));
  unset($schema['views_view']['unique key']);
  return $schema;
}

/**
 *
 */
function views_update_6003() {
  db_add_unique_key('views_view', 'name', array('name'));
}

/**
 * Enlarge the views_object_cache.data column to prevent truncation and JS
 * errors.
 */
function views_schema_6004() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_object_cache']['fields']['data']['type'] = 'text';
  $schema['views_object_cache']['fields']['data']['size'] = 'big';
  return $schema;
}

/**
 *
 */
function views_update_6004() {
  $new_field = array(
    'type' => 'text',
    'size' => 'big',
    'description' => 'Serialized data being stored.',
    'serialize' => TRUE,
  );

  // Drop and re-add this field because there is a bug in
  // db_change_field that causes this to fail when trying to cast the data.
  db_drop_field('views_object_cache', 'data');
  db_add_field('views_object_cache', 'data', $new_field);
}

/**
 * Enlarge the base_table column.
 */
function views_schema_6005() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_view']['fields']['base_table']['length'] = 64;
  return $schema;
}

/**
 *
 */
function views_update_6005() {
  $new_field = array(
    'type' => 'varchar',
    'length' => '64',
    'default' => '',
    'not null' => TRUE,
    'description' => 'What table this view is based on, such as node, user, comment, or term.',
  );
  db_change_field('views_view', 'base_table', 'base_table', $new_field);
}

/**
 * Add the cache_views_data table to support standard caching.
 */
function views_schema_6006() {
  $schema = views_schema(__FUNCTION__);
  $schema['cache_views_data'] = drupal_get_schema_unprocessed('system', 'cache');
  $schema['cache_views_data']['description'] = 'Cache table for views to store pre-rendered queries, results, and display output.';
  $schema['cache_views_data']['fields']['serialized']['default'] = 1;
  return $schema;
}

/**
 *
 */
function views_update_6006() {
  $table = drupal_get_schema_unprocessed('system', 'cache');
  $table['description'] = 'Cache table for views to store pre-rendered queries, results, and display output.';
  $table['fields']['serialized']['default'] = 1;

  db_create_table('cache_views_data', $table);
}

/**
 * Add aggregate function to PostgreSQL so GROUP BY can be used to force only
 * one result to be returned for each item.
 */
function views_update_6007() {
  if (Database::getConnection()->databaseType() == 'pgsql') {
    db_query('CREATE OR REPLACE FUNCTION first(anyelement, anyelement) RETURNS anyelement AS \'SELECT COALESCE($1, $2);\' LANGUAGE \'sql\';');
    db_query("DROP AGGREGATE IF EXISTS first(anyelement)");
    db_query("CREATE AGGREGATE first(sfunc = first, basetype = anyelement, stype = anyelement);");
  }
}

/**
 * Add the primary key to views_display table.
 */
function views_schema_6008() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_display']['primary key'] = array('vid', 'id');
  return $schema;
}

/**
 * Add the primary key to the views_display table.
 */
function views_update_6008() {
  db_add_primary_key('views_display', array('vid', 'id'));
}

/**
 * Enlarge the views_display.display_options field to accommodate a larger set
 * of configurations (e. g. fields, filters, etc.) on a display.
 */
function views_schema_6009() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_display']['fields']['display_options'] = array(
    'type' => 'text',
    'size' => 'big',
    'description' => 'A serialized array of options for this display; it contains options that are generally only pertinent to that display plugin type.',
    'serialize' => TRUE,
    'serialized default' => 'a:0:{}',
  );
  return $schema;
}

/**
 *
 */
function views_update_6009() {
  $schema = views_schema_6009();

  if (Database::getConnection()->databaseType() == 'pgsql') {
    db_query('ALTER TABLE {views_display} RENAME "display_options" TO "display_options_old"');
    db_add_field('views_display', 'display_options', $schema['views_display']['fields']['display_options']);

    $sql = "SELECT vid, id, display_options_old FROM {views_display}";
    $result = db_query($sql);
    foreach ($result as $row) {
      $row['display_options_old'] = $row['display_options_old'];
      $sql = "UPDATE {views_display} SET display_options = :display_optons WHERE vid = :vid AND id = :id";
      db_query($sql, array(
        ':display_optons' => $row['display_options_old'],
        ':vid' => $row['vid'],
        ':id' => $row['id'],
      ));
    }

    db_drop_field('views_display', 'display_options_old');
  }
  else {
    db_change_field('views_display', 'display_options', 'display_options', $schema['views_display']['fields']['display_options']);
  }
}

/**
 * Remove the view_php field.
 */
function views_schema_6010() {
  $schema = views_schema(__FUNCTION__);
  unset($schema['views_view']['fields']['view_php']);
  unset($schema['views_view']['fields']['is_cacheable']);
  return $schema;
}

/**
 * Remove the view_php and is_cacheable field.
 */
function views_update_6010() {
  db_drop_field('views_view', 'view_php');
  db_drop_field('views_view', 'is_cacheable');
}

/**
 * Remove views_object_cache table and move the data to ctools_object_cache.
 */
function views_schema_6011() {
  $schema = views_schema(__FUNCTION__);
  unset($schema['views_object_cache']);
  return $schema;
}

/**
 * Remove views_object_cache table and move the data to ctools_object_cache.
 */
function views_update_6011() {
  $caches = db_query("SELECT * FROM {views_object_cache}");
  foreach ($caches as $item) {
    drupal_write_record('ctools_object_cache', $item);
  }
  db_drop_table('views_object_cache');
}

/**
 * Correct the cache setting for exposed filter blocks.
 *
 * @see http://drupal.org/node/910864
 */
function views_update_6012() {
  // There is only one simple query to run.
  db_update('blocks')
    ->condition('module', 'views')
    ->condition('delta', db_like('-exp-') . '%', 'LIKE')
    ->fields(array('cache' => DRUPAL_NO_CACHE));
}

/**
 * Add a human readable name.
 */
function views_schema_6013() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_view']['fields']['human_name'] = array(
    'type' => 'varchar',
    'length' => '255',
    'default' => '',
    'description' => 'A human readable name used to be displayed in the admin interface',
  );
  return $schema;
}

/**
 *
 */
function views_update_6013() {
  // Check because D6 installs may already have added this.
  if (!db_field_exists('views_view', 'human_name')) {

    $new_field = array(
      'type' => 'varchar',
      'length' => '255',
      'default' => '',
      'description' => 'A human readable name used to be displayed in the admin interface',
    );

    db_add_field('views_view', 'human_name', $new_field);
  }
}

/**
 *
 */
function views_schema_6014() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_view']['fields']['core'] = array(
    'type' => 'int',
    'default' => 0,
    'description' => 'Stores the drupal core version of the view.',
  );
  return $schema;
}

/**
 * Add a drupal core version field.
 */
function views_update_6014() {
  // Check because D6 installs may already have added this.
  if (!db_field_exists('views_view', 'core')) {
    $new_field = array(
      'type' => 'int',
      'default' => 0,
      'description' => 'Stores the drupal core version of the view.',
    );
    db_add_field('views_view', 'core', $new_field);
  }
}

/**
 * Rename some system variables.
 */
function views_update_7000() {
  // Views now lets users turn off query details on live preview.
  $query_on_top = variable_get('views_ui_query_on_top');
  if (isset($query_on_top)) {
    variable_set('views_ui_show_sql_query', TRUE);
    if ($query_on_top) {
      variable_set('views_ui_show_sql_query_where', 'above');
    }
    else {
      variable_set('views_ui_show_sql_query_where', 'below');
    }
    variable_del('views_ui_query_on_top');
  }

  // Rename the views_hide_help_message variable from negative to positive.
  $hide_help = variable_get('views_hide_help_message');
  if (isset($hide_help)) {
    variable_set('views_ui_show_advanced_help_warning', !$hide_help);
    variable_del('views_hide_help_message');
  }

  // Rename the unused views_no_hover_links variable.
  variable_del('views_no_hover_links');
}

/**
 * Fix missing items from Views administrative breadcrumb.
 */
function views_update_7001() {
  $depth = db_select('menu_links')
    ->fields('menu_links', array('depth'))
    ->condition('link_path', 'admin/structure/views/view/%')
    ->execute()
    ->fetchField();

  if ($depth == 3) {
    db_delete('menu_links')
      ->condition('link_path', 'admin/structure/views/%', 'LIKE')
      ->execute();
    cache_clear_all(NULL, 'cache_menu');
    menu_rebuild();
  }
}

/**
 *
 */
function views_schema_7300() {
  return views_schema_6013();
}

/**
 * Make sure the human_name field is added to the views_view table.
 *
 * If you updated from 6.x-2.x-dev to 7.x-3.x you already had schema
 * version 6014, so update 6013 never was nor will be run. As a result,
 * the human_name field is missing from the database.
 *
 * This will add the human_name field if it doesn't already exist.
 */
function views_update_7300() {
  views_update_6013();
}

/**
 *
 */
function views_schema_7301() {
  $schema = views_schema(__FUNCTION__);
  $schema['views_view']['fields']['name']['length'] = 128;
  return $schema;
}

/**
 * Enlarge the name column.
 */
function views_update_7301() {
  $new_field = array(
    'type' => 'varchar',
    'length' => '128',
    'default' => '',
    'not null' => TRUE,
    'description' => 'The unique name of the view. This is the primary field views are loaded from, and is used so that views may be internal and not necessarily in the database. May only be alphanumeric characters plus underscores.',
  );
  db_change_field('views_view', 'name', 'name', $new_field);
}

/**
 * Remove headers field from cache tables.
 *
 * @see system_update_7054()
 */
function views_update_7302() {
  if (db_field_exists('cache_views', 'headers')) {
    db_drop_field('cache_views', 'headers');
  }
  if (db_field_exists('cache_views_data', 'headers')) {
    db_drop_field('cache_views_data', 'headers');
  }
}
