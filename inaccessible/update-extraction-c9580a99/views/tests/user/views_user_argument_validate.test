<?php

/**
 * @file
 * Definition of ViewsUserArgumentValidate.
 */

/**
 * Tests views user argument argument handler.
 */
class ViewsUserArgumentValidate extends ViewsSqlTest {

  /**
   *
   */
  public static function getInfo() {
    return array(
      'name' => 'Tests user argument validator',
      'description' => 'Tests user argument validator',
      'group' => 'Views Plugins',
    );
  }

  /**
   * {@inheritdoc}
   */
  public function setUp(array $modules = array()) {
    $modules[] = 'views';
    parent::setUp($modules);

    $this->account = $this->drupalCreateUser();
  }

  /**
   *
   */
  public function testArgumentValidateUserUid() {
    $account = $this->account;
    // Test 'uid' case.
    $view = $this->view_argument_validate_user('uid');
    $view->set_display('default');
    $view->pre_execute();
    $view->init_handlers();
    $this->assertTrue($view->argument['null']->validate_arg($account->uid));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a string variable since type is 'uid'.
    $this->assertFalse($view->argument['null']->validate_arg($account->name));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a valid numeric, but for a user that doesn't exist.
    $this->assertFalse($view->argument['null']->validate_arg(32));
  }

  /**
   *
   */
  public function testArgumentValidateUserName() {
    $account = $this->account;
    // Test 'name' case.
    $view = $this->view_argument_validate_user('name');
    $view->set_display('default');
    $view->pre_execute();
    $view->init_handlers();
    $this->assertTrue($view->argument['null']->validate_arg($account->name));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a uid variable since type is 'name'.
    $this->assertFalse($view->argument['null']->validate_arg($account->uid));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a valid string, but for a user that doesn't exist.
    $this->assertFalse($view->argument['null']->validate_arg($this->randomName()));
  }

  /**
   *
   */
  public function testArgumentValidateUserEither() {
    $account = $this->account;
    // Test 'either' case.
    $view = $this->view_argument_validate_user('either');
    $view->set_display('default');
    $view->pre_execute();
    $view->init_handlers();
    $this->assertTrue($view->argument['null']->validate_arg($account->name));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a uid variable since type is 'name'.
    $this->assertTrue($view->argument['null']->validate_arg($account->uid));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a valid string, but for a user that doesn't exist.
    $this->assertFalse($view->argument['null']->validate_arg($this->randomName()));
    // Reset safed argument validation.
    $view->argument['null']->argument_validated = NULL;
    // Fail for a valid uid, but for a user that doesn't exist.
    $this->assertFalse($view->argument['null']->validate_arg(32));
  }

  /**
   *
   */
  public function view_argument_validate_user($argtype) {
    $view = new view();
    $view->name = 'view_argument_validate_user';
    $view->description = '';
    $view->tag = '';
    $view->view_php = '';
    $view->base_table = 'node';
    $view->is_cacheable = FALSE;
    $view->api_version = 2;
    $view->disabled = FALSE; /* Edit this to true to make a default view disabled initially */

    /* Display: Master */
    $handler = $view->new_display('default', 'Master', 'default');
    $handler->display->display_options['access']['type'] = 'none';
    $handler->display->display_options['cache']['type'] = 'none';
    $handler->display->display_options['exposed_form']['type'] = 'basic';
    $handler->display->display_options['pager']['type'] = 'full';
    $handler->display->display_options['style_plugin'] = 'default';
    $handler->display->display_options['row_plugin'] = 'fields';
    /* Argument: Global: Null */
    $handler->display->display_options['arguments']['null']['id'] = 'null';
    $handler->display->display_options['arguments']['null']['table'] = 'views';
    $handler->display->display_options['arguments']['null']['field'] = 'null';
    $handler->display->display_options['arguments']['null']['style_plugin'] = 'default_summary';
    $handler->display->display_options['arguments']['null']['default_argument_type'] = 'fixed';
    $handler->display->display_options['arguments']['null']['validate']['type'] = 'user';
    $handler->display->display_options['arguments']['null']['validate_options']['type'] = $argtype;
    $handler->display->display_options['arguments']['null']['must_not_be'] = 0;

    return $view;
  }

}
