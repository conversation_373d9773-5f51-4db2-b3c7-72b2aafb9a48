<?php

/**
 * @file
 * Definition of ViewsUserArgumentDefault.
 */

/**
 * Tests views user argument default plugin.
 */
class ViewsUserArgumentDefault extends ViewsSqlTest {

  /**
   *
   */
  public static function getInfo() {
    return array(
      'name' => 'Tests user argument default plugin',
      'description' => 'Tests user argument default plugin',
      'group' => 'Views Plugins',
    );
  }

  /**
   *
   */
  public function test_plugin_argument_default_current_user() {
    // Create a user to test.
    $account = $this->drupalCreateUser();

    // Switch the user, we have to check the global user too, because
    // drupalLogin is only for the simpletest browser.
    $this->drupalLogin($account);
    global $user;
    $admin = $user;
    drupal_save_session(FALSE);
    $user = $account;

    $view = $this->view_plugin_argument_default_current_user();

    $view->set_display('default');
    $view->pre_execute();
    $view->init_handlers();

    $this->assertEqual($view->argument['null']->get_default_argument(), $account->uid, 'Uid of the current user is used.');
    // Switch back.
    $user = $admin;
    drupal_save_session(TRUE);
  }

  /**
   *
   */
  public function view_plugin_argument_default_current_user() {
    $view = new view();
    $view->name = 'test_plugin_argument_default_current_user';
    $view->description = '';
    $view->tag = '';
    $view->view_php = '';
    $view->base_table = 'node';
    $view->is_cacheable = FALSE;
    $view->api_version = '3.0-alpha1';
    $view->disabled = FALSE; /* Edit this to true to make a default view disabled initially */

    /* Display: Master */
    $handler = $view->new_display('default', 'Master', 'default');
    $handler->display->display_options['access']['type'] = 'none';
    $handler->display->display_options['cache']['type'] = 'none';
    $handler->display->display_options['exposed_form']['type'] = 'basic';
    $handler->display->display_options['pager']['type'] = 'full';
    $handler->display->display_options['pager']['options']['items_per_page'] = '10';
    $handler->display->display_options['pager']['options']['offset'] = '0';
    $handler->display->display_options['pager']['options']['id'] = '0';
    $handler->display->display_options['style_plugin'] = 'default';
    $handler->display->display_options['row_plugin'] = 'fields';
    /* Field: Content: Title */
    $handler->display->display_options['fields']['title']['id'] = 'title';
    $handler->display->display_options['fields']['title']['table'] = 'node';
    $handler->display->display_options['fields']['title']['field'] = 'title';
    $handler->display->display_options['fields']['title']['alter']['alter_text'] = 0;
    $handler->display->display_options['fields']['title']['alter']['make_link'] = 0;
    $handler->display->display_options['fields']['title']['alter']['trim'] = 0;
    $handler->display->display_options['fields']['title']['alter']['word_boundary'] = 1;
    $handler->display->display_options['fields']['title']['alter']['ellipsis'] = 1;
    $handler->display->display_options['fields']['title']['alter']['strip_tags'] = 0;
    $handler->display->display_options['fields']['title']['alter']['html'] = 0;
    $handler->display->display_options['fields']['title']['hide_empty'] = 0;
    $handler->display->display_options['fields']['title']['empty_zero'] = 0;
    $handler->display->display_options['fields']['title']['link_to_node'] = 0;
    /* Argument: Global: Null */
    $handler->display->display_options['arguments']['null']['id'] = 'null';
    $handler->display->display_options['arguments']['null']['table'] = 'views';
    $handler->display->display_options['arguments']['null']['field'] = 'null';
    $handler->display->display_options['arguments']['null']['default_action'] = 'default';
    $handler->display->display_options['arguments']['null']['style_plugin'] = 'default_summary';
    $handler->display->display_options['arguments']['null']['default_argument_type'] = 'current_user';
    $handler->display->display_options['arguments']['null']['must_not_be'] = 0;

    return $view;
  }

}
