/*a:link           { color: #000000; text-decoration: none }
a:visited        { color: #000000; text-decoration: none }
*/
a:active         { color: #000000; text-decoration: underline }
a:hover          { color: #000000; text-decoration: underline }
.topmenu         { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 9pt; color:#000000}
.cmslinks	 { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; color: red; font-size: 8pt}
.title           { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 13pt; font-weight: bold}
.path            { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 12pt; font-weight: bold}
.pathsmall       { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; font-weight: bold}
.menu            { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 11pt; font-weight: bold}
.submenu         { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; font-weight: bold}
.main            { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; color:#000000}
.mainsmall       { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 9pt; color:#000000; white-space: nowrap;}
.mainlarge       { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 11pt; color:#000000}
.mainbold        { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; font-weight: bold}
.mainerror       { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; color: #FF0000}
.input           { font-family: Courier; font-size: 12pt}
.foundlink       { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; font-weight: bold}
.foundcontent    { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 8pt}
.searchkey       { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 10pt; color: #FF0000 }
.footer          { font-family: Arial,Helvetica,Geneva,Swiss,SunSans-Regular; font-size: 7pt; line-height: 12pt}
.button	         { padding: 0px; margin: 0px; color: #000000; font-size: 8pt; border-top: 1px #000000 solid; border-left: 1px #000000 solid; border-right: 1px #000000 solid; border-bottom: 1px #000000 solid; }
.button2         { padding: 0px; margin: 0px; color: #000000; font-size: 10pt; border-top: 1px #000000 solid; border-left: 1px #000000 solid; border-right: 1px #000000 solid; border-bottom: 1px #000000 solid; }
.button3 {
    border-radius: 10px;
    text-decoration: none;
    background-color: #EEEEEE;
    color: #333333;
    padding: 2px 6px 2px 6px;
    border-top: 1px solid #CCCCCC;
    border-right: 1px solid #333333;
    border-bottom: 1px solid #333333;
    border-left: 1px solid #CCCCCC;
}
.dimmed { color:gray }
.language        { font-family: Courier; font-size: 9pt}
.main:link       { color: #000000; text-decoration: none }
.main:visited    { color: #000000; text-decoration: none }
.main:active     { color: #000000; text-decoration: underline }
a.main:hover      { color: #000000; text-decoration: underline }
.topmenu:link       { color: #000000; text-decoration: none }
.topmenu:visited    { color: #000000; text-decoration: none }
.topmenu:active     { color: #000000; text-decoration: underline }
.topmenu:hover      { color: #000000; text-decoration: underline }

#stats tr:hover, #request tr:hover, #events tr:hover {
    background-color: lightcyan !important;
}

table#request .infopopup {
    display: none;
}
table#request tr:hover .infopopup {
    display: block;
}

#stats td {
    border: 1px dotted;
}
#stats .number {
    text-align: right;
}
#stats th {
    text-align: left;
    vertical-align: bottom;
}
#stats a {
    text-decoration: underline;
}
.hilite {
    background-color: yellow;
}

#editOwner input[type="text"] {
    width: 300px;
}

#outer {
    margin: auto;
}
#outer > tbody > tr > td {
    vertical-align: top;
}

#innerLeft, #innerRight {

}
#innerLeft input {
    width: 200px;
}
.timeField {
    text-align: center;
    width: 40px !important;
}
.editRequest {
    font-family:Lucida Grande;
}
#left {
    display: inline-block;
}
#right {
    float: right;
    position: relative;
    top: 0px;
    width: 66%;
}
#special-request{
    width: 500px;
}

.number {
    text-align: right;
}
#kwartaal {
    float:right;
}
#jaren table {
    position: relative;
    left: 1px;
    top: 1px;
}
.centered {
    position: fixed;
    top: 50%;
    left: 50%;
    /* bring your own prefixes */
    transform: translate(-50%, -50%);
}

.jaar{
    background-color: lightgray;
}
.jaar td:first-child {
    padding-left: 40px;
}
.kwartaal{
    background-color: lightgreen;
}
.kwartaal td:first-child {
    padding-left: 80px;
}
.maand{
    background-color: lightblue;
}
.maand td:first-child, .week td:first-child {
    padding-left: 120px;
}
.week{
    background-color: lightsalmon;
    cursor: auto;
}
.dag{
    background-color: lightgoldenrodyellow;
    cursor: auto;
}
.dag td:first-child {
    padding-left: 180px;
    text-align:right;
}
#jaren > td:first-child {
    white-space: nowrap;
}
#jaren {
    border: 1px dotted lightgrey;
    cursor: pointer;
}
#invoice tr:last-child td{
    border: none;
}
#invoice td {
    border: 1px dotted;
    padding: 5px;
}
td.copiedRequest{background-color: lightgreen;}

#arr2html {
    font-size: 14pt;
    white-space: nowrap;
}
#arr2html th{
    text-align: left;
}
#arr2html td {
    border-right: 1px black dotted;
    border-bottom: 1px black dotted;
}
.even {
    background-color: #F2F2F2;
}
.chooseList {
    margin: 10px;
    white-space: nowrap;
}
.chooseList th {
    padding-right: 10px;
}
.chooseList tr:hover:nth-child(n+2) {
    background-color: lightblue ;
    cursor: pointer;
}
.chooseList td {
    border: 1px solid #dee2e6;
    padding: 4px;
}
.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 1px dotted black;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 100%;
    left: 50%;
    margin-left: -60px;
    /* Fade in tooltip - takes 1 second to go from 0% to 100% opac: */
    opacity: 0;
    transition: opacity 1s;
}

.tooltipsmall .tooltiptext {
    margin-left: -100px;
    width: 200px;
}

.tooltip:hover .tooltiptext, .tooltipsmall:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
    overflow-wrap: break-word;
    white-space: normal;
}
.inconsistent {
    background-color: red;
}
.price {
    background-color: green;
    color: white !important;
    text-align: center;
}
.price input {
    text-align: right;
    width: 75px;    
}
.notsailedreason {
    background-color: red;
    color: white;
    text-align: center;
}

/* Custom rule for table font size */
#request td, #special-request td {
    font-size: 12px;
}
