<?php
/*
 * * Main index file hated by coen but loved by robe<PERSON><PERSON> because he is not crazy
 */

include_once('lib.array2.php');

$mail = null;
$replace_captain = '';
$replace_contactfirstname = '';
$replace_sex = '';
$replace_day = '';
$replace_captain = '';
$replace_contact = '';
$price_crewboat = '';
$quotation = '';

$mode = isset( $_GET[ 'mode' ] ) ? $_GET[ 'mode' ] : null;
$answer = isset( $_GET[ 'answer' ] ) ? $_GET[ 'answer' ] : null;
$tab = isset( $_GET[ 'tab' ] ) ? $_GET[ 'tab' ] : 'all';
$user = isset( $_GET[ 'user' ] ) ? $_GET[ 'user' ] : 'rdc';
$page = isset( $_GET[ 'page' ] ) ? $_GET[ 'page' ] : 1;

if ( !empty( $_POST ) ) {
    $SendAnEmail = isset( $_POST[ 'sendmail' ] ) ? $_POST[ 'sendmail' ] : 'yes';
    $submit = isset( $_POST[ 'submit' ] ) ? $_POST[ 'submit' ] : "";
    $reply = isset( $_POST[ 'reply' ] ) ? $_POST[ 'reply' ] : "";
    $mode = isset( $_POST[ 'mode' ] ) ? $_POST[ 'mode' ] : "";
    $subject = isset( $_POST[ 'subject' ] ) ? $_POST[ 'subject' ] : "";
    $answer = isset( $_POST[ 'answer' ] ) ? $_POST[ 'answer' ] : "";
}
?>
<!DOCTYPE html>
<HTML>
    <HEAD>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title><?= $_SERVER[ 'HTTP_HOST' ] ?> - Mailer</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="bn_style.css">
        <SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
            function begin() {
                if (document.form0)
                    document.form0.elements[0].focus();
            }
            function checkAll() {
                checkboxes = document.getElementsByClassName('selectRows');
                for (var i = 0, n = checkboxes.length; i < n; i++) {
                    checkboxes[i].checked = !checkboxes[i].checked;
                }
            }
            function copySlots() {
                // Get the text field
                var slots = document.getElementById("slots");
                var copyText = slots.innerHTML;
                var copyText = copyText.replace(/<br\s*[\/\?]?>/gi, "\n");
                // Copy the text inside the element
                navigator.clipboard.writeText(copyText);
            }
        </SCRIPT>
        <script src="https://cloud.tinymce.com/5/tinymce.min.js?apiKey=is9fcbiynuvqeqzw5dih1vnrb3icwgjd2n98y8xdshhw5o0o"></script>
        <script>tinymce.init({
                selector: 'textarea', // change this value according to your HTML
                plugins: ["advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table contextmenu paste"],
                relative_urls: false,
                remove_script_host: false,
                content_css: "bn_tinyMCE.css"
            });</script>
    </HEAD>
    <BODY class="container-fluid" onLoad="begin();">
        <?php
        require("bn_common2.php");

        $tpl_date = 'd-m-Y H:i';

        // make mailer header						
        echo mailerTabs(); // Coen 2015-08-20
        if ( !isset( $id ) ) {
            ?>
            <form method="post" action="<?= ROOT ?>/bn_admin/gotoRequest.php" class="mb-3">
                <input type="hidden" name="user" value="<?= $user ?>">
                <input type="hidden" name="page" value="<?= $page ?>">
                <input type="hidden" name="tab" value="<?= $tab ?>">
                <div class="row">
                    <div class="col-auto">
                        <label for="search" class="col-form-label">Go to request (email or number):</label>
                    </div>
                    <div class="col-auto">
                        <input id="search" name="search" type="text" class="form-control" autofocus="">
                    </div>
                </div>
            </form>
            <?php
        }
        $table_id = "request";
        if ( isset( $id ) ) {
            $table_id = "special-" . $table_id;
        }
        ?>
        <div id="left"> <!-- left -->
            <?php
            if ( !isset( $id ) ) {
                ?>
                <form method="post" action="checkAvailability.php">
                    <input type="hidden" name="user" value="<?= $user ?>">
                    <?php
                }
                ?>
                <div class="table-responsive">
                <table id="<?= $table_id ?>" class="table table-striped table-hover table-sm">

                    <?php
                    $overview_str = "";
                    $dayofweek[ 'nl' ] = array('zo', 'ma', 'di', 'wo', 'do', 'vr', 'za');
                    $dayofweek[ 'en' ] = array('Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa');
                    $dayofweek[ 'de' ] = array('So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa');
                    $id_to_formtype = array(1 => 'sailing cruises', 'sailing cruises 2', 'bareboat sailing-yachts', 'yachts with crew', 'storage', 'buy a yacht', 'sell a yacht');
                    $formtype_to_id = array_flip( $id_to_formtype );
                    $formid_to_table = array(1 => 'bn_order_cruise', 'bn_order_cruise2', 'bn_order_bareboat', 'bn_order_crewboat', 'bn_order_storage', 'bn_order_buy', 'bn_order_sell');
                    $id_to_subject = array(1 => 'Form title: Sailing cruises', 'Form title: Sailing cruises', 'Form title: Bareboat sailing-yachts', 'Form title: Yachts with crew', 'Form title: Storage', 'Form title: Buy a yacht', 'Form title: Sell a yacht');
                    $id_to_message = array(1 => 'Form message: Sailing cruises', 'Form message: Sailing cruises', 'Form message: Bareboat sailing-yachts', 'Form message: IA+', 'Form message: Storage', 'Form message: Buy a yacht', 'Form message: Sell a yacht');
                    // Here the values are given for the records in the translation table that form the buttons for editing some texts
                    $id_to_tdbkey = array(1 => '292', '288', '216', '366', '222', '233', '262');


                    $id2contact = shipid2contactEmail();
                    $orderNr2orderInfo = orderNr2orderInfo(); // array met order info strings
                    $shipid2Rederij = shipid2Rederij();
                    $Rederij2sailedPerc = Rederij2sailedPerc();

                    $data = getAllShips();
                    foreach ($data as $row) {
                        $ship_id = $row[ 'field_boat_number_value' ];

                        $id_to_boatname[ $ship_id ] = $row[ 'title' ];    // bootnaam
                        $id_to_type[ $ship_id ] = $row[ 'name' ];     // type
                        $id_to_day[ $ship_id ] = $row[ 'field_area_value' ];  // dag
                        $id_to_night[ $ship_id ] = $row[ 'field_bathrooms_value' ]; // nacht
                        $id_to_avail[ $ship_id ] = $row[ 'field_avail_url_url' ]; // beschikbaarheidsurl
                        $id_to_contact[ $ship_id ] = $row[ 'field_customer_value' ]; // contact

                        if ( isset( $id2contact[ $ship_id ] ) ) {
                            $id_to_company[ $ship_id ] = array('company' => $id2contact[ $ship_id ][ 'company' ],
                                'email' => $id2contact[ $ship_id ][ 'email' ],
                                'telefoon' => $id2contact[ $ship_id ][ 'telefoon' ]
                            ); // company name, email en telefoon
                        }
                    }
// if order-ID is unknown display the request list
                    if ( !isset( $id ) ) {
                        //######### Hier begint het tonen van overzicht requests ############
                        // construct the order table for desktop
                        $header = array("<input type='checkbox' class='form-check-input' onclick=checkAll()>", 'BN', 'L', 'From', 'Sail', '#', 'Day', 'Night', 'Boot Name', 'Code', 'SucRte', 'Event', 'Received', 'E-mail', 'Phone', '?', '!', '+', '-', 'C', '<>', 'O', '€', 'Last action', 'UR', 'OR', 'NO', 'Cp', 'Del', 'Ed');
                            $field = array('img', 'id', 'language', 'lname', 'startt', 'perso', 'day', 'night', 'subject', 'code', 'SucRte', 'event', 'date_in', 'email', 'phon1', 'yesorno', 'yesplus', 'yes', 'no', 'cancel', 'incontact', 'remOwner', 'paid', 'date_out', 'CTRL_sailed_yes_or_no', 'CHK_Amount', 'CHK_WhyFail', 'cp', 'del', 'ed');

// read the order data
                        $query_all = "SELECT * FROM bn_requests ORDER BY ID DESC LIMIT " . ($config['mailer_maxposts'] * ($page - 1)) . ', ' . $config['mailer_maxposts'];
                        $querycount_all = 'SELECT ID FROM bn_requests';

                        $query_neg = "SELECT * FROM bn_requests WHERE yesplus > 0 AND date(startt) <= CURDATE() ORDER BY startt DESC LIMIT " . ($config['mailer_maxposts'] * ($page - 1)) . ', ' . $config['mailer_maxposts'];
                        $querycount_neg = 'SELECT ID FROM bn_requests WHERE yesplus > 0';

                        switch ($tab):
                            case 'all':
                                $query = $query_all;
                                $querycount = $querycount_all;
                                break;
                            case 'neg':
                                $query = $query_neg;
                                $querycount = $querycount_neg;
                                break;
                        endswitch;
                        $result = recordsFromQuery( $query );
                        $resultcount = recordsFromQuery( $querycount );
                        $num_records = count( $resultcount );

                        $page_count = ceil( $num_records / $config[ 'mailer_maxposts' ] );
                        $previous_link = ($page - 1) <= 0 ? "<li class='page-item disabled'><a class='page-link' href='#'>Previous</a></li>" : "<li class='page-item'><a class='page-link' href='$PHP_SELF?user=$user&tab=$tab&page=" . ($page - 1) . "'>Previous</a></li>";
                        $next_link = ($page * $config[ 'mailer_maxposts' ]) >= $num_records ? "<li class='page-item disabled'><a class='page-link' href='#'>Next</a></li>" : "<li class='page-item'><a class='page-link' href='$PHP_SELF?user=$user&tab=$tab&page=" . ($page + 1) . "'>Next</a></li>";

                        $pagination_html = "<nav><ul class='pagination pagination-sm'>{$previous_link}";
                        if ($page_count > 1) {
                            $pagination_html .= "<li class='page-item'><div class='input-group input-group-sm' style='width: 150px;'><select class='form-select' onchange=\"window.location.href='$PHP_SELF?user=$user&tab=$tab&page=' + this.value\">";
                            for ($i = 1; $i <= $page_count; $i++) {
                                $selected = $i == $page ? "selected" : "";
                                $pagination_html .= "<option value='$i' $selected>$i</option>";
                            }
                            $pagination_html .= "</select><span class='input-group-text'>of $page_count</span></div></li>";
                        }
                        $pagination_html .= "{$next_link}</ul></nav>";


                        $overview_str .= "<thead><tr class='table-light'><th>" . implode("</th><th>", $header) . "</th></tr></thead><tbody>";
                        // fetch orders
                        foreach ($result as $myrow) {
                            if ( !isset( $first_id ) ) {
                                $first_id = $myrow[ 'ID' ];
                            }
                            $replied = ($myrow[ 'date_out' ] != '');
                            $row_classes = [];
                            $row_title = '';
                            if ( isset( $id_to_company[ $myrow[ 'shipid' ] ] ) ) {
                                $row_title = 'title="' . implode( " * ", $id_to_company[ $myrow[ 'shipid' ] ] ) . '"';
                            }

                            $overview_str .= "<tr {$row_title}>";

                            reset( $field );

                            // Fill field values for the table

                            $emailArr = shipid2contactEmail( $myrow[ 'shipid' ] );
                            foreach ($field as $key => $value) {
                                $tmp_field = '';
                                $cell_class = '';
                                switch ($value) {
                                    case 'img':
                                        $icon = getStatusIcon( $myrow[ 'ID' ] );
                                        if ( !empty( $icon ) ) {
                                            $tmp_field = "<img src='../img/" . $icon . "' style='width: 35px'>";
                                        }
                                        break;
                                    case 'id':
                                        $tmp_field = 'BN' . str_pad( $myrow[ 'ID' ], 7, "0", STR_PAD_LEFT );
                                        break;
                                    case 'subject':
                                        if ( isset( $id_to_boatname[ $myrow[ 'shipid' ] ] ) )
                                            $tmp_field = "{$id_to_boatname[ $myrow[ 'shipid' ] ]}" . " (" . $myrow[ 'shipid' ] . ")"; // coen
                                        break;
                                    case 'day':
                                        if ( isset( $id_to_day[ $myrow[ 'shipid' ] ] ) )
                                            $tmp_field = "{$id_to_day[ $myrow[ 'shipid' ] ]}";
                                        break;
                                    case 'night':
                                        if ( isset( $id_to_night[ $myrow[ 'shipid' ] ] ) )
                                            $tmp_field = "{$id_to_night[ $myrow[ 'shipid' ] ]}";
                                        break;
                                    case 'code':
                                        $company = shipid2CompanyContact( $myrow[ 'shipid' ] );
                                        $tdb9 = $company[ 'tdb9' ];
                                        $tmp_field = 'N/A';
                                        if ( !empty( $emailArr[ $myrow[ 'shipid' ] ][ 'code' ] ) ) {
                                            $tmp_field = $emailArr[ $myrow[ 'shipid' ] ][ 'code' ];
                                        }
                                        $tmp_field = "<a href='editOwner.php?user=" . $user . "&tab=" . $tab . "&tdb9=" . $tdb9 . "'>" . $tmp_field . "</a>";
                                        break;
                                    case 'SucRte':
                                        $shipid = $myrow[ 'shipid' ];
                                        $sailedPerc = "N/A";
                                        if ( isset( $shipid2Rederij[ $shipid ] ) && isset( $Rederij2sailedPerc[ $shipid2Rederij[ $shipid ] ] ) ) {
                                            $sailedPerc = $Rederij2sailedPerc[ $shipid2Rederij[ $shipid ] ][ 'sailedperc' ];
                                        }
                                        $sailedPerc = str_pad( $sailedPerc, 3, "0", STR_PAD_LEFT ) . "&#37;";
                                        $tmp_field = $sailedPerc;
                                        break;
                                    case 'date_in':
                                        $tmp_field = date( $tpl_date, strtotime( $myrow[ 'date_in' ] ) );
                                        break;
                                    case 'yesorno':
                                        $tmp_field = ($myrow[ 'yesorno' ] == 1 ? '?' : '');
                                        break;
                                    case 'yesplus':
                                        $tmp_field = ($myrow[ 'yesplus' ] > 0 ? $myrow[ 'yesplus' ] : '');
                                        break;
                                    case 'yes':
                                        $tmp_field = ($myrow[ 'yes' ] == 1 ? '+' : '');
                                        break;
                                    case 'no':
                                        $tmp_field = ($myrow[ 'no' ] == 1 ? '-' : '');
                                        break;
                                    case 'cancel':
                                        $tmp_field = ($myrow[ 'cancel' ] == 1 ? 'C' : '');
                                        break;
                                    case 'incontact':
                                        $tmp_field = ($myrow[ 'incontact' ] == 1 ? '<>' : '');
                                        break;
                                    case 'paid':
                                        $tmp_field = (!empty($myrow['Ontvangen_betaling_datum']) ? '€' : '');
                                        break;
                                    case 'remOwner':
                                        $tmp_field = (!empty( $myrow[ 'maybeAvailReason' ] ) ? "<div title='" . $myrow[ 'maybeAvailReason' ] . "'>O</div>" : "");
                                        break;
                                    case 'date_out':
                                        if ( $myrow[ 'date_out' ] ) {
                                            $tmp_field = date( $tpl_date, strtotime( $myrow[ 'date_out' ] ) );
                                        }
                                        break;
                                    case 'CHK_Amount':
                                        $tmp_field = ($myrow[ 'CHK_Amount' ] > 0 ? $myrow[ 'CHK_Amount' ] : '');
                                        break;
                                    case 'startt':
                                        if ( $myrow[ 'startt' ] ) {
                                            $tmp_field = getEventCode( $myrow[ 'startt' ], $myrow[ 'startp' ] );
                                            $tmp_field = isset($tmp_field['code']) ? $tmp_field['code'] : ''; // Coen changed fcn getEventCode 0404-2022, returns array now
                                            if ( empty( $tmp_field ) ) {
                                                $tmp_field = time2str( $myrow[ 'startt' ] );
                                            }
                                        } else {
                                            $tmp_field = "--INCOMPLETE--";
                                        }
                                        break;
                                    case 'cp':
                                        $tmp_field = '++';
                                        break;
                                    case 'del':
                                        $tmp_field = '&nbsp;X';
                                        break;
                                    case 'ed':
                                        $tmp_field = "<img src='../img/pen.svg' width='15'>";
                                        break;
                                    case "CTRL_sailed_yes_or_no":
                                        if ( is_null( $myrow[ 'CTRL_sailed_yes_or_no' ] ) ) {
                                            $tmp_field = '?';
                                        } elseif ( $myrow[ 'CTRL_sailed_yes_or_no' ] == 1 ) {
                                            $tmp_field = 'v';
                                        } else {
                                            $tmp_field = '-';
                                        }
                                        break;
                                    default:
                                        $tmp_field = $myrow[ $value ];
                                        break;
                                }
                                if ( !$tmp_field ) {
                                    $tmp_field = '&nbsp;';
                                }

                                $addCheckbox = FALSE;
                                if ( !$replied ) {
                                    $cell_class .= ' fw-bold';
                                    $addCheckbox = TRUE;
                                }

                                if ( $replied 
                                    AND empty( $myrow[ 'yesplus' ] ) 
                                    AND empty( $myrow[ 'yes' ] )
                                    AND empty( $myrow[ 'no' ] )
                                    AND empty( $myrow[ 'cancel' ] )
                                    AND empty( $myrow[ 'incontact' ] )
                                   ) 
                                {
                                    if ( strtotime( $myrow[ 'startt' ] ) < time() ) {
                                        $cell_class .= ' text-muted';
                                    } else {
                                        $cell_class .= ' text-danger fw-bold';
                                        $addCheckbox = TRUE;
                                    }
                                }
                                if (
                                        is_null( $myrow[ 'startt' ] ) || // half a form
                                        strtotime( $myrow[ 'startt' ] ) < time() || // in the past
                                        $myrow[ 'email' ] == "<EMAIL>" || // no client email
                                        !$emailArr || // ???? no contact of skipper?
                                        date( "Y-m-d", strtotime( $myrow[ 'date_out' ] ) ) == date( "Y-m-d" ) || // today??
                                        $myrow[ 'cancel' ] == '1' || // canceled request
                                        !empty( $myrow[ 'maybeAvailReason' ] ) ) { // consult the owner first
                                    $addCheckbox = FALSE;
                                }
                                if ( $addCheckbox && $value == "img" ) {
                                    $tmp_field .= "<input class='selectRows form-check-input' name='checked[" . $myrow[ 'ID' ] . "]' type='checkbox'>";
                                }

                                if ( ($myrow[ 'CTRL_sailed_yes_or_no' ] == '1') AND ( $myrow[ 'CHK_WhyFail' ] != "") ) {
                                    $cell_class .= ' text-success';
                                }
                                if ( ($myrow[ 'CTRL_sailed_yes_or_no' ] == '1') AND ( $myrow[ 'CHK_WhyFail' ] == "") ) {
                                    $cell_class .= ' text-primary fw-bold';
                                }
                                if ( $myrow[ 'CHK_Amount' ] > 0 ) {
                                    $cell_class .= ' text-success fw-bold';
                                }
                                if ( ($myrow[ 'no' ] == 1) AND ( $myrow[ 'yesplus' ] == '') ) {
                                    $cell_class .= ' text-muted';
                                }
                                if ( $myrow[ 'cancel' ] == 1 ) {
                                    $cell_class .= ' text-muted';
                                }
                                if ( $myrow[ 'incontact' ] == 1 ) {
                                    $cell_class .= ' text-muted';
                                }

                                if ( ($value == 'cp') OR ( $value == 'ed') OR ( $value == 'id') OR ( $value == 'del') ) { // cp ed del or id
                                    if ( $value == 'cp' ) {
                                        $url = "copyRequest.php?user=" . $user . "&action=cp&id=" . $myrow[ 'ID' ] . "&page=" . $page . "&tab=" . $tab;
                                        $td_class = $myrow[ 'copiedRequest' ] ? " class='copiedRequest'" : "";
                                        $overview_str .= "<td{$td_class}><a href='" . $url . "'>$tmp_field</a></td>";
                                    } elseif ( $value == 'ed' ) {
                                        $url = "editRequest.php?id=" . $myrow[ 'ID' ] . "&user=" . $user . "&page=" . $page . "&tab=" . $tab;
                                        $overview_str .= "<td><a href='" . $url . "'>$tmp_field</a></td>";
                                    } elseif ( $value == 'del' ) {
                                        $url = "delRequest.php?id=" . $myrow[ 'ID' ] . "&user=" . $user . "&page=" . $page . "&tab=" . $tab;
                                        $overview_str .= "<td><a href='" . $url . "'>$tmp_field</a></td>";
                                    } else { // id
                                        $url = "http://mail.google.com/mail/#search/" . 'BN' . str_pad( $myrow[ 'ID' ], 7, "0", STR_PAD_LEFT );
                                        $overview_str .= "<td class='fw-bold'><a href='" . $url . "' target='_blank'>$tmp_field</a></td>";
                                    }
                                } else {
                                    if ( $value != 'email' ) {
                                        $url = "$PHP_SELF?user=$user&page=$page&tab=$tab&id={$myrow[ 'ID' ]}";
                                    } else {
                                        $url = "mailto:{$myrow[ $value ]}";
                                        if ( strlen( $myrow[ $value ] ) > 18 ) {
                                            $tmp_field = str_replace( $myrow[ 'email' ], substr( $myrow[ 'email' ], 0, 15 ) . '...', $tmp_field );
                                        }
                                    }
                                    if ( $value != 'img' ) {
                                        $tooltip = "";
                                        if ( $value == 'CHK_WhyFail' && $myrow[ 'CHK_WhyFail' ] != "" && isset( $orderNr2orderInfo[ $myrow[ 'CHK_WhyFail' ] ] ) ) {
                                            $tooltip = "title='" . $orderNr2orderInfo[ $myrow[ 'CHK_WhyFail' ] ] . "'";
                                        }
                                        $overview_str .= "<td {$tooltip} class='{$cell_class}'><a href=\"$url\">$tmp_field</a></td>";
                                    } else {
                                        $overview_str .= "<td class='{$cell_class}'>$tmp_field</td>";
                                    }
                                }
                            }
                            $overview_str .= '</tr>';
                            $last_id = $myrow[ 'ID' ];
                        }
                        $overview_str .= "</tbody>";
                        $overview_str .= "<tfoot><tr><td colspan='" . count($header) . "'>{$pagination_html}</td></tr></tfoot>";
                        echo($overview_str);
                    } else {    // display an individual message and, if present, reply
                        // ####### Hier begint het tonen van 1 request #### //
                        if ( isset( $submit ) && isset( $mode ) ) {
                            // ## FORM SUBMITTED ## //
                            if ( $mode == 'check' ) {
                                $tmp_field = 'yesorno';
                            } else {
                                $tmp_field = $answer;
                            }
                            // clear other status fields when setting new values. rdc 26-8-2015
                            $tmp_field2 = '';
                            $tmp_field3 = '';
                            $tmp_field4 = '';
                            //obtaining the name of requestor
                            $query = "SELECT fname, lname \n                                    FROM   bn_requests \n                                    WHERE  id = $id";
                            $myrow_order = oneRecordFromQuery( $query );
                            $temp_name = $myrow_order[ 'fname' ] . " " . $myrow_order[ 'lname' ];

                            switch ($tmp_field) {
                                case "yesorno":
                                    $tmp_field2 = 'yesplus';
                                    $tmp_field3 = 'yes';
                                    $tmp_field4 = 'no';
                                    logEvent( 7, $temp_name, $id, "" );
                                    break;
                                case "yesplus":
                                    $tmp_field2 = 'yes';
                                    $tmp_field3 = 'no';
                                    $tmp_field4 = 'no';
                                    setSolvedNoAlternative( $id );
                                    logEvent( 9, $temp_name, $id, "" );
                                    break;
                                case "yes":
                                    $tmp_field2 = 'yesplus';
                                    $tmp_field3 = 'no';
                                    $tmp_field4 = 'no';
                                    logEvent( 10, $temp_name, $id, "" );
                                    break;
                                case "no":
                                    $tmp_field2 = 'yesplus';
                                    $tmp_field3 = 'yes';
                                    $tmp_field4 = 'yes';
                                    logEvent( 13, $temp_name, $id, "" );
                                    break;
                                default:
                                    die( 'Error!!!! tmp_field can not be ' . $tmp_field );
                            }

                            $query = "UPDATE bn_requests \n                            SET date_out = \"" . date( "Y-m-d H:i:s" ) . "\",\n                                $tmp_field = 1,\n                                $tmp_field2 = NULL,\t\t\t\t\t\n                                $tmp_field3 = NULL,\n                                $tmp_field4 = NULL\n                            WHERE id = $id";
                            //echo($query);
                            //die();
                            //* query
                            //mailtest
                            //$result = mysql_query($query, $conn) or die("Can not write request data to table");
                            doQuery( $query );
                            // ## EINDE FORM SUBMITTED (hieronder staat nog een stuk) ## //
                        }
                        // ## individuele aanvraag weergeven
                        $query = "SELECT * \n                                    FROM   bn_requests \n                                    WHERE  id = $id";
                        //$result = mysql_query($query, $conn) or die("Query failed");
                        //$myrow_order = mysql_fetch_array($result, MYSQL_ASSOC);
                        $myrow_order = oneRecordFromQuery( $query );
                        $formtype = $id_to_formtype[ $myrow_order[ 'order_type' ] ];
                        $yesorno = $myrow_order[ 'yesorno' ];
                        $yesplus = $myrow_order[ 'yesplus' ];
                        $yes = $myrow_order[ 'yes' ];
                        $no = $myrow_order[ 'no' ];
                        $status = ($yesorno ? '?' : '') . ($yesplus ? '!' : '') . ($yes ? '+' : '') . ($no ? '-' : '');

                        // create the order number: format BNxxxxxxxxx
                        $replace_bnnummer = 'BN' . str_pad( $myrow_order[ 'ID' ], 7, "0", STR_PAD_LEFT );

                        // deze waardes zijn speciaal voor de huursloep en het formulier
                        if (bn_isSelfCollect($id)) {
                            $replace_deadline = date('d-m-Y H:i', strtotime($myrow_order['Deadline']));
                            $replace_totaalbedrag = format_price($myrow_order['Bedrag_totaal'] ?? 0, 'nl');

                            $query = "SELECT deposit, tax FROM bn_huursloeplocatie WHERE shipid = " . $myrow_order['shipid'];
                            $record = oneRecordFromQuery($query);
                            $replace_borg = $myrow_order['Aantal_boten'] * $record['deposit'];
                            $replace_tax = bn_calcTax($myrow_order['shipid'], $myrow_order['perso'], $myrow_order['Aantal_boten']);
                            $replace_huurbedrag = $myrow_order['Bedrag_totaal'] - $replace_borg - $replace_tax;
                            $replace_tax = format_price($replace_tax, 'nl');
                            $replace_tan = $myrow_order['TAN'];
                            $replace_paymentlink = paymentURL($id, TRUE);
                        }

                        if ( $id_to_company[ $myrow_order[ 'shipid' ] ][ 'company' ] == 'huursloep' ) {
                            $specialcompany = ' HUURSLOEP';
                        }

                        $l_form = $myrow_order[ 'language' ];
                        //$replied = ($myrow_order['date_out'] != '');
                        $skipper = shipid2contactEmail( $myrow_order[ 'shipid' ] );
                        $skipper = $skipper[ $myrow_order[ 'shipid' ] ];
                        $replied = false;
                        if ( isset( $submit ) && isset( $mode ) ) {
                            // ## FORM SUBMITTED ## //
                            // Coen 12-05-2015, alleen mail sturen wanneer radio btn sendmail op Yes staat RDC: != 'no' van gemaakt 
                            if ( $SendAnEmail != 'no' ) {
                                $myrow_order[ 'mail' ] = $reply;
                                $myMail = new BNmailer();
                                $myMail->SetFromRequest();
                                if ( $mode == "check" ) {
                                    // send it to skipper
                                    $myMail->AddAddress( $skipper[ 'email' ], $skipper[ 'name' ] );
                                } else {
                                    //send it to client
                                    $myMail->AddAddress( $myrow_order[ 'email' ], $myrow_order[ 'fname' ] . " " . $myrow_order[ 'lname' ] );
                                }
                                $myMail->AddBCC( $myMail->From );
                                $myMail->Subject = stripslashes( $subject );
                                $myMail->Body = stripslashes( $reply );
                                if ( !$myMail->Send() ) {
                                    error_log( $myMail->ErrorInfo );
                                }

                                // De gegevens kunnen naar de schipper
                                if ( $answer == 'yesplus' ) {
                                    $skipperMail = new BNmailer();
                                    $skipperMail->SetFromRequest();
                                    $skipperMail->AddAddress( $skipper[ 'email' ], $skipper[ 'name' ] );
                                    $skipperMail->AddBCC( $skipperMail->From );
                                    $skipperMail->Subject = stripslashes( $subject );
                                    // there is a bug in tinymce that wraps ul in ul, THAT'S WHY
                                    $skipperMail->Body = $reply_captain;
                                    if ( !$skipperMail->Send() ) {
                                        error_log( $skipperMail->ErrorInfo );
                                    }
                                }
                            }
                            $mode = '';
                            header( 'Location: ' . $PHP_SELF . "?user=$user&page=$page&tab=$tab" ); // EXIT go back to list
                            exit;
                            // ## EINDE FORM SUBMITTED ## //
                        }

                        $query = "SELECT * \n              FROM   bn_requests \n              WHERE  id = {$myrow_order[ 'ID' ]}";
                        //echo $query;
                        $myrow_order_detail = oneRecordFromQuery( $query, TRUE, TRUE );
                        // construct mailform    
                        $mail .= "<TR><TD CLASS=main COLSPAN=2 VALIGN=TOP>\n\t\t<A CLASS=main HREF=$PHP_SELF?user=$user&page=$page&tab=$tab" . (isset( $mode ) ? "&id=$id" : '') . "><U>Back</U></A>" . ($replied || isset( $mode ) ? '' : " &nbsp; &nbsp;\n\t\t<A CLASS=main HREF='" . ROOT . "/bn_admin/bn_ownerMail.php?user=$user&page=$page&tab=$tab&id=$id'><FONT COLOR=" . ($yesorno ? 'CCCCCC' : '000000') . "><U>Check (+/-)</U></FONT></A> &nbsp; &nbsp;<A CLASS=main HREF='$PHP_SELF?user=$user&page=$page&tab=$tab&id=$id&mode=reply&answer=yesplus'><FONT COLOR=" . ($yesplus ? 'CCCCCC' : '000000') . "><U>Reply (+!)</U></FONT></A> &nbsp; &nbsp;\n\t\t<A CLASS=main HREF=$PHP_SELF?user=$user&page=$page&tab=$tab&id=$id&mode=reply&answer=yes><FONT COLOR=" . ($yes ? 'CCCCCC' : '000000') . "><U>Reply (+)</U></FONT></A> &nbsp; &nbsp;\n\t\t<A CLASS=main HREF=$PHP_SELF?user=$user&page=$page&id=$id&mode=reply&answer=no><FONT COLOR=" . ($no ? 'CCCCCC' : '000000') . "><U>Reply (-)</U></FONT></A>\n\t\t<!--A CLASS=main HREF=$PHP_SELF?user=$user&page=$page&id=$id&mode=reply&answer=quotation><FONT COLOR=" . ($quotation ? 'CCCCCC' : '000000') . "><U>Reply (Quotation)</U></FONT></A>--> ") .
                                "" . '\n\t\t<a href=cancelBoat.php?user=' . $user . '&page=' . $page . '&tab=' . $tab . '&id=' . $id . ' class=main><u>Cancel 1</u></a>&nbsp; &nbsp;&nbsp;<a href=cancelRequest.php?user=' . $user . '&page=' . $page . '&tab=' . $tab . '&id=' . $id . ' class=main><u>Cancel all</u></a></td></tr>\n                <!--TD CLASS=main ALIGN=LEFT VALIGN=TOP>' . (isset( $mode ) ? '&nbsp;' : "<Edit:&nbsp;<A CLASS=main HREF=\"http://www.bootnodig.nl/bn_dev/tdb/bn_databases.php?tdbkey=" . ($id_to_tdbkey[ $myrow_order[ 'order_type' ] ] + 2) . "&tdbname=bn_translation&sort=key&order=asc&word=&action=edit\" TARGET=_blank><U>(+/-)&nbsp;check</U></A>&nbsp;&nbsp;&nbsp;<A CLASS=main HREF=\"http://www.bootnodig.nl/bn_dev/tdb/bn_databases.php?tdbkey=" . ($id_to_tdbkey[ $myrow_order[ 'order_type' ] ] + 3) . "&tdbname=bn_translation&sort=key&order=asc&word=&action=edit\" TARGET=_blank><U>(+!)&nbsp;reply</U></A>&nbsp;&nbsp;&nbsp;<A CLASS=main HREF=\"http://www.bootnodig.nl/bn_dev/tdb/bn_databases.php?tdbkey={$id_to_tdbkey[ $myrow_order[ 'order_type' ] ]}&tdbname=bn_translation&sort=key&order=asc&word=&action=edit\" TARGET=_blank><U>(+)&nbsp;reply</U></A>&nbsp;&nbsp;&nbsp;<A CLASS=main HREF=\"http://www.bootnodig.nl/bn_dev/tdb/bn_databases.php?tdbkey=" . ($id_to_tdbkey[ $myrow_order[ 'order_type' ] ] + 1) . "&tdbname=bn_translation&sort=key&order=asc&word=&action=edit\" TARGET=_blank><U>(-)&nbsp;reply</U></A>&nbsp;&nbsp;&nbsp;<A CLASS=main HREF=\"http://www.bootnodig.nl/bn_dev/tdb/bn_databases.php?tdbkey=181&tdbname=bn_config&sort=key&order=asc&word=&action=edit\" TARGET=_blank><U><FONT STYLE=\"background-color:yellow\"><B>bn#</B></FONT></U></A>") . '</TD--></TR>';
                        $mail .= "<TR><TD COLSPAN=3><FORM NAME=form0 ACTION=$PHP_SELF METHOD=POST><IMG SRC='../img/pixtrans.gif' WIDTH=1 HEIGHT=7</TD></TR>";
                        $menu_bar = $mail;
                        $mail .= "<TR><TH CLASS=main ALIGN=LEFT BGCOLOR=CCCCCC>From:</TD><TD COLSPAN=2 CLASS=main BGCOLOR=CCCCCC><A CLASS=main HREF=\"mailto:{$myrow_order[ 'email' ]}\">{$myrow_order[ 'fname' ]} {$myrow_order[ 'lname' ]} - {$myrow_order[ 'oname' ]}</A></TD>\n                            <td CLASS=main><A HREF='editRequest.php?user=$user&page=$page&tab=$tab&id=$id'><img src='../img/pen.svg' style='width: 50px;'></a></td></TR>";
                        $mail .= '<TR><TH CLASS=main ALIGN=LEFT BGCOLOR=CCCCCC>Date:</TD><TD COLSPAN=2 CLASS=main BGCOLOR=CCCCCC>' . date( $tpl_date, strtotime( $myrow_order[ 'date_in' ] ) ) . '</TD></TR>';
                        //$mail .= "<TR><TH CLASS=main ALIGN=LEFT BGCOLOR=CCCCCC>Subject:&nbsp;</TD><TD COLSPAN=2 CLASS=main BGCOLOR=CCCCCC>{$translation_form[($id_to_subject[$myrow_order['order_type']])][$l_form]}</TD></TR>";
                        $mail .= "<TR><TH CLASS=main ALIGN=LEFT BGCOLOR=CCCCCC>Subject:&nbsp;</TD>\n            <TD COLSPAN=2 CLASS=main BGCOLOR=CCCCCC>REQUEST - {BOOT}</TD></TR>";
                        $pixtrans = '<TR><TD CLASS=main><IMG SRC="../img/pixtrans.gif" WIDTH=60 HEIGHT=0></TD><TD CLASS=main><IMG SRC="../img/pixtrans.gif" WIDTH=295 HEIGHT=0></TD><TD CLASS=MAIN></TR>';
                        $mail .= $pixtrans;

                        $mysql_order = array('First Name', 'Last Name', 'Company', 'Address', 'Zip code', 'City', 'State, province or region', 'Country', 'E-mail', 'Phone I', 'Phone II', 'Fax', 'Comments');
                        $order_fields = array('fname', 'lname', 'oname', 'address', 'zipcode', 'city', 'state', 'country', 'email', 'phon1', 'phon2', 'fax', 'remark');

                        $tmp_line = str_repeat( '=', strlen( $translation_form[ 'Information request' ][ $l_form ] ) + 9 );

                        $content = "{$translation_form[ 'Information request' ][ $l_form ]} {BNNUMMER}\r\n$tmp_line\r\n";
                        $content .= $translation_form[ 'Request date' ][ $l_form ] . ":  " . "{AANVRAAGDATUM}" . "\r\n";
                        // hier moet het veld gelegenhied bij komen!
                        $all_fields_titles = array('Yacht type', 'Yacht', 'Departure from', 'Return to', 'From (date)', 'Until', 'Number of persons', 'First Name', 'Last Name', 'Company', 'E-mail', 'Phone I (e.g. +30 22810 86184)', 'Phone II (e.g. +30 22810 86184)', 'Occasion', 'Comments');
                        $select_fields = array('Yacht type', 'Yacht');
                        $date_fields = array('From (date)', 'Until');
                        $all_fields_mysql = array('yacht_type', 'shipid', 'startp', 'endp', 'startt', 'endt', 'perso', 'fname', 'lname', 'oname', 'email', 'phon1', 'phon2', 'event', 'remark');
                        $popup_var = "l=$l_form&o=4&id={$myrow_order_detail[ 'shipid' ]}&days={DAYS}";
                        $popup_id = $myrow_order_detail[ 'shipid' ];

                        $gondel_options = array();
                        foreach ($data as $row) {
                            $tmp_option = "{$row[ 'title' ]}" . " (" . $translation_form[ 'day' ][ $l_form ] . ": {$row[ 'field_area_value' ]}, " . $translation_form[ 'night' ][ $l_form ] . ": " . ($row[ 'field_bathrooms_value' ] ? $row[ 'field_bathrooms_value' ] : '0') . ")";
                            switch ($row[ 'name' ]) {
                                case 'Klipper':
                                    $klipper_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Schoener':
                                    $schoener_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Tjalk':
                                    $tjalk_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Aak':
                                    $aak_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Skûtsje':
//            skutsje is stuk deze regel wordt nooit bereikt:
                                    $skutsje_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Botter':
                                    $botter_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Kotter':
                                    $kotter_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                /* case 'Scow':
                                  $schouw_options[$row['field_boat_number_value']] = $tmp_option;
                                  break; */
                                case 'Statenjacht':
                                    $statenjacht_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Zeiljacht':
                                    $zeiljacht_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Rondvaartboot':
                                    //echo "<br>HALLO<br \>". $row['field_boat_number_value']. "    ". $tmp_option. "<br><br>" ;
                                    $rondvaartboot_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Salonboot':
                                    $salonboot_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Partyboot':
                                    $partyschip_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Motorschip':
                                    $motorschip_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Sloep':
                                    $sloep_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Catamaran':
                                    $catamaran_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Woonboot':
                                    $woonboot_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                case 'Gondel':
                                    $gondel_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                                    break;
                                default;
                                    $skutsje_options[ $row[ 'field_boat_number_value' ] ] = $tmp_option;
                            }
                            //if ($data['id'] == $myrow_order_detail['shipid']) {
                            if ( isset( $id_to_boatname[ $myrow_order_detail[ 'shipid' ] ] ) ) {
                                $myrow_order_detail[ 'yacht_type' ] = $translation_form[ $id_to_type[ $myrow_order_detail[ 'shipid' ] ] ][ $l_form ];
                                $replace_boat = $id_to_boatname[ $myrow_order_detail[ 'shipid' ] ];
                                $replace_customer = $id_to_contact[ $myrow_order_detail[ 'shipid' ] ];
                            }
                        }
                        $yacht_options = $klipper_options + $schoener_options + $tjalk_options + $aak_options + $skutsje_options + $botter_options + $kotter_options + $statenjacht_options + $zeiljacht_options + $rondvaartboot_options + $salonboot_options + $partyschip_options + $motorschip_options + $sloep_options + $catamaran_options + $woonboot_options + $gondel_options;

                        $output_title[ 'Phone I (e.g. +30 22810 86184)' ] = 'Phone I';
                        $output_title[ 'Phone II (e.g. +30 22810 86184)' ] = 'Phone II';
                        $output_title[ 'Fax (e.g. + 30 22810 86186)' ] = 'Fax';
                        $output_title[ 'Length' ] = 'Length (feet)';
                        $output_title[ 'Width' ] = 'Width (feet)';
                        $output_title[ 'Maintenance, repair or transfer (please specify)' ] = 'Maintenance, repair or transfer';
                        $output_title[ 'Cruise (please specify)' ] = 'Cruise';
                        if ( in_array( 'Type', $all_fields_titles ) ) {
                            $tmp_options = explode( '+', $config[ 'form_options_type' ] );
                            $i = 1;
                            foreach ($tmp_options as $key => $value) {
                                //while (list($key, $val) = each( $tmp_options )) {
                                $type_options[ $i ] = "Option: $val";
                                ++$i;
                            }
                        }

                        /* create empty arrays in order to avoid the following error in case of non-existent arrays:
                          Warning: in_array() [function.in-array]: Wrong datatype for second argument
                         */
                        if ( !$select_fields ) {
                            $select_fields = array("");
                        }
                        if ( !$date_fields ) {
                            $date_fields = array("");
                        }
                        if ( !isset( $checkbox_fields ) ) {
                            $checkbox_fields = array("");
                        }
                        if ( !isset( $float_fields ) ) {
                            $float_fields = array("");
                        }

                        //ppr($all_fields_titles);
                        //die();
                        for ($i = 0; $i < count( $all_fields_titles ); ++$i) {
                            $tmp_field_title = $all_fields_titles[ $i ];
                            $tmp_field_mysql = $all_fields_mysql[ $i ];
                            if ( in_array( $tmp_field_title, $select_fields ) ) {
                                $field_type = 'select';
                            } else {
                                if ( in_array( $tmp_field_title, $date_fields ) ) {
                                    $field_type = 'date';
                                } else {
                                    if ( in_array( $tmp_field_title, $checkbox_fields ) ) {
                                        $field_type = 'checkbox';
                                    } else {
                                        if ( in_array( $tmp_field_title, $float_fields ) ) {
                                            $field_type = 'float';
                                        } else {
                                            $field_type = 'normal';
                                        }
                                    }
                                }
                            }
                            //ppr ($output_title);
                            if ( isset( $output_title[ $tmp_field_title ] ) ) {
                                $tmp_field_title = $output_title[ $tmp_field_title ];
                            }
                            if ( $tmp_field_title != 'Cruise/Period' ) {
                                if ( $mode == 'check' ) {
                                    if ( in_array( $tmp_field_mysql, $order_fields ) && $tmp_field_mysql != 'remark' ) {
                                        //
                                    } else {
                                        $content .= "{$translation_form[ $tmp_field_title ][ $l_form ]}:  ";
                                    }
                                } else {
                                    $content .= "{$translation_form[ $tmp_field_title ][ $l_form ]}:  ";
                                }
                            }
                            switch ($field_type) {
                                case 'select':
                                    switch ($tmp_field_title) {
                                        case 'Type':
                                            $content .= $translation_form[ ($type_options[ $myrow_order_detail[ $tmp_field_mysql ] ]) ][ $l_form ] . "\r\n";
                                            $reply_info[ 'Type' ] = $translation_form[ ($type_options[ $myrow_order_detail[ $tmp_field_mysql ] ]) ][ $l_form ];
                                            break;
                                        case 'Category':
                                            $content .= $translation_form[ ($category_options[ $myrow_order_detail[ $tmp_field_mysql ] ]) ][ $l_form ] . "\r\n";
                                            $reply_info[ 'Category' ] = $translation_form[ ($category_options[ $myrow_order_detail[ $tmp_field_mysql ] ]) ][ $l_form ];
                                            break;
                                        case 'Period':
                                            break;

                                        case 'Sailing-boat type':
                                            $content .= $myrow_order_detail[ $tmp_field_mysql ] . "\r\n";
                                            $reply_info[ 'Sailing-boat type' ] = $myrow_order_detail[ $tmp_field_mysql ];
                                            break;
                                        case 'Sailing-boat':
                                            $content .= $sailingboat_options[ $myrow_order_detail[ $tmp_field_mysql ] ] . "\r\n";
                                            $reply_info[ $tmp_field_title ] = $sailingboat_options[ $myrow_order_detail[ $tmp_field_mysql ] ];
                                            $tmp_prices = $price_bareboat[ $myrow_order_detail[ $tmp_field_mysql ] ];
                                            break;
                                        case 'Yacht type':
                                            //print $myrow_order_detail[$tmp_field_mysql] . "\r\n";
                                            //ppr ($myrow_order_detail);
                                            //die();
                                            $content .= $myrow_order_detail[ $tmp_field_mysql ] . "\r\n";
                                            $reply_info[ 'Yacht type' ] = $myrow_order_detail[ $tmp_field_mysql ];
                                            break;
                                        case 'Yacht':
                                            $content .= $yacht_options[ $myrow_order_detail[ $tmp_field_mysql ] ] . "\r\n";
                                            $reply_info[ $tmp_field_title ] = $yacht_options[ $myrow_order_detail[ $tmp_field_mysql ] ];
                                            //$reply_info['Price per day'] = $price_crewboat[$myrow_order_detail[$tmp_field_mysql]];
                                            //ppr($yacht_options);
                                            //ppr($price_crewboat);
                                            break;
                                    }
                                    break;
                                case 'date':
                                    $dateTimeTemp = explode( ' ', $myrow_order_detail[ $tmp_field_mysql ] );
                                    list($year, $month, $day) = explode( '-', $dateTimeTemp[ 0 ] );
                                    list($hour, $minute, $seconds) = explode( ':', $dateTimeTemp[ 1 ] );
                                    $mktime = mktime( (int) $hour, (int) $minute, 0, (int) $month, (int) $day, (int) $year );
                                    $date = date( 'd-m-Y H:i', $mktime );
                                    $date_r = getdate( $mktime );
                                    $content .= "$date ({$dayofweek[ $l_form ][ $date_r[ 'wday' ] ]})\r\n";
                                    $reply_info[ $tmp_field_title ] = $date;
                                    $reply_date[ $tmp_field_title ] = $mktime;
                                    break;
                                case 'checkbox':
                                    $content .= ($myrow_order_detail[ $tmp_field_mysql ] == 1 ? $translation_form[ 'yes' ][ $l_form ] : $translation_form[ 'no' ][ $l_form ]) . "\r\n";
                                    if ( $checkbox_options[ $tmp_field_mysql ] ) {
                                        if ( $myrow_order_detail[ $tmp_field_mysql ] == 1 ) {
                                            $data = $checkbox_options[ $tmp_field_mysql ];
                                            $tmp_calculation = ($data[ 'unit' ] == 'day' ? $data[ 'rate' ] * $myrow_order_detail[ 'days' ] : $data[ 'rate' ] / 7 * $myrow_order_detail[ 'days' ]);
                                            $reply_info[ 'Options' ] .= ($reply_info[ 'Options' ] ? "\r\n" : '') . "{$translation_form[ ucfirst( $data[ 'name' ] ) ][ $l_form ]} (" . ($data[ 'rate' ] != 0 ? "Euro {$data[ 'rate' ]}/{$translation_form[ $data[ 'unit' ] ][ $l_form ]}" : $translation_form[ 'free service' ][ $l_form ]) . "): Euro " . format_price( $tmp_calculation, $l_form );
                                            $reply_info[ 'Cost of options' ] += $tmp_calculation;
                                        }
                                    }
                                    break;
                                case 'float':
                                    $content .= format_float( $myrow_order_detail[ $tmp_field_mysql ], $l_form ) . "\r\n";
                                    $reply_info[ 'Price per day' ] = format_float( $myrow_order_detail[ $tmp_field_mysql ], $l_form );
                                    break;
                                default:
                                    if ( in_array( $tmp_field_mysql, $order_fields ) ) {
                                        if ( $mode == 'check' && $tmp_field_mysql != 'remark' ) {
                                            //
                                        } else {
                                            $content .= $myrow_order[ $tmp_field_mysql ] . "\r\n";
                                            $reply_info[ $tmp_field_title ] = $myrow_order[ $tmp_field_mysql ];
                                        }
                                    }
                                    else {
                                        $content .= $myrow_order_detail[ $tmp_field_mysql ] . "\r\n";
                                        $reply_info[ $tmp_field_title ] = $myrow_order_detail[ $tmp_field_mysql ];
                                    }
                                    break;
                            }
                        }
                        $content .= $tmp_line . "\r\n\r\n";
                        $content = str_replace( "{AANVRAAGDATUM}", date( "d-m-Y", strtotime( $myrow_order[ 'date_in' ] ) ), $content );
                        //$content = '';
                        if ( $mode == 'check' ) {
                            $content = requestInfo( $myrow_order[ 'ID' ], FALSE );
                        } else {
                            $content = requestInfo( $myrow_order[ 'ID' ] );
                        }
                        //e($content);
                        $content_html = $content;
                        $content_html = str_replace( chr( 128 ), '&euro;', $content_html );

                        // toegevoegd
                        $replace_fromtime = str_replace( '/', '-', $reply_info[ 'From (date)' ] );
                        $replace_until = str_replace( '/', '-', $reply_info[ 'Until' ] );


                        if ( substr( $replace_fromtime, 0, 10 ) == substr( $replace_until, 0, 10 ) ) {
                            // Booking is for a day
                            $replace_day = 'true';
                            $replace_period = $replace_fromtime . ' - ' . substr( $replace_until, 11, 5 );
                        } else {
                            // Booking spans more than one day
                            $replace_period = $replace_fromtime . ' - ' . $replace_until;
                        }
                        $mail .= "<TR><TD id='content-html' class='main' COLSPAN=3>$content_html</TD></TR>";
                        //############# 23072021 Coen show availability of Huursloep only in future requests to minimise sloepdelen api calls
                        if ( in_array( $myrow_order[ 'shipid' ], $shipidsHuursloep ) ) {
                            $mail .= "<tr><td><b>Availability:</b>";
                            $mail .= "<tr>";
                            if ( strtotime( $myrow_order[ 'startt' ] ) >= time() ) {
                                $times = bn_GetPickupTimesLetsBook( $myrow_order[ 'ID' ] );
                                $startt = date( "H:i", strtotime( $myrow_order[ 'startt' ] ) );
                                //var_dump( is_int( array_search( $startt, array_column( $times, "time" )) ) );
                                $bAvail = is_int( array_search( $startt, array_column( $times, "time" ) ) );
                                if ( empty( $times ) ) {
                                    $mail .= "<td>No availability today</td>";
                                } elseif ( (bool) $bAvail ) {
                                    $mail .= "<td>Available</td>";
                                } else {
                                    $mail .= "<tr><td>Alternative(s) available:</td></tr>";
                                    $mail .= "<td id='slots'>";
                                    foreach ($times as $time) {
                                        $mail .= $time[ 'time' ] . "-" . $time[ 'end' ] . "<br>";
                                    }
                                    $mail .= "</td>";
                                    $mail .= "<td valign='top'>\n                                        <button type='button' onclick='copySlots()'>Copy Slots</button>\n                                        </td>";
                                }
                            } else {
                                $mail .= "<td>Trip in the past</td></tr>";
                            }
                        } else {
                            $mail .= "<tr><td>Not&nbsp;huursloep</td></tr>";
                        }
                        //########### End Availability of Huursloep
                        $contactAr = shipid2CompanyContact( $myrow_order_detail[ 'shipid' ] );
                        $mail .= ("<TR><TD><b>Owner:</b></TD><TD class='main' COLSPAN=3><a href=editOwner.php?user=" . $user . "&tab=" . $tab . "&tdb9=" . $contactAr[ 'tdb9' ] . " class='btn btn-primary btn-sm'>Edit <img src='../img/pen.svg'  width='15'></a></TD></TR>");
                        $mail .= ("<TR><TD class='main' COLSPAN=3><a href='" . ROOT . "/bn_owner/bn_owner_avail.php?code=" . $contactAr[ 'pwcode' ] . "' target=_blank>" . $contactAr[ 'company' ] . "</a></TD></TR>");
                        $mail .= ("<TR><TD class='main' COLSPAN=3>" . $contactAr[ 'name' ] . "</TD></TR>");
                        $mail .= ("<TR><TD class='main' COLSPAN=3>" . $contactAr[ 'Remark' ] . "</TD></TR>");
                        $mail .= ("<TR><TD class='main' COLSPAN=3>" . $contactAr[ 'email' ] . "</TD></TR>");
                        $mail .= ("<TR><TD class='main' COLSPAN=3>" . $contactAr[ 'telefoon' ] . "</TD></TR>");
                        $mail .= "<TR><TD class='main' COLSPAN=3><b>Log:</b></TD></TR>";
                        $mail .= ("<TR><TD id='history' class='main' COLSPAN=3>" . requestHistoryByID( $myrow_order[ 'ID' ] ) . "</TD></TR>");
                        // 6 april 18: added btn to save domain name to db to clean up client history, gmail.com is not unique
                        $domain = getDomainFromEmail( $myrow_order[ 'email' ] );
                        $mail .= "<TR><TD class='main' COLSPAN=1><br><br><b>Client history:</b></TD><td style='text-align:center'>";
                        if ( in_array( $domain, getBNmailDomains() ) ) {
                            // fake disabled link with a span
                            $mail .= "<span class='btn btn-secondary btn-sm disabled'>Add <b>$domain</b> to mail domains</span>";
                        } else {
                            $mail .= "<a href='insertDomain.php?domain=$domain' class='btn btn-primary btn-sm'>Add <b>$domain</b> to mail domains</a>";
                        }
                        $mail .= ("</td></TR><TR><TD id='client_history' class='main' COLSPAN=3>" . arr2html( getClientHistory( $myrow_order[ 'ID' ] ) ) . "</TD></TR>");
                        $replied = false;
                        if ( $replied ) {
                            
                        } else {
                            if ( in_array( $mode, array('reply', 'check') ) ) {
                                switch ($formtype) {
                                    case 'yachts with crew':

                                        $search = array(
                                            '{URL}'
                                            , '{NAAM}'
                                            , '{DATE}'
                                            , '{DATE IN}'
                                            , '{YACHT}'
                                            , '{DEPARTURE FROM}'
                                            , '{RETURN TO}'
                                            , '{DATE FROM}'
                                            , '{DATE UNTIL}'
                                            , '{NUMBER OF PERSONS}'
                                            , '{INFORMATIEAANVRAAG}'
                                            , '{%EMAIL_FOOTER%}'
                                        );
                                        $replace = array(
                                            GetURL( $myrow_order[ 'shipid' ] ),
                                            trim( $myrow_order[ 'fname' ] ) . " " . trim( $myrow_order[ 'lname' ] ),
                                            date( 'd-m-Y' ),
                                            date( 'd-m-Y', strtotime( $myrow_order[ 'date_in' ] ) ),
                                            $reply_info[ 'Yacht type' ] . ': ' . $reply_info[ 'Yacht' ],
                                            $reply_info[ 'Departure from' ],
                                            $reply_info[ 'Return to' ],
                                            $reply_info[ 'From (date)' ],
                                            $reply_info[ 'Until' ],
                                            $reply_info[ 'Number of persons' ],
                                            $content,
                                            getEmailFooter( $myrow_order[ 'language' ] )
                                        );
                                        $name = $myrow_order[ 'fname' ] . " " . trim( $myrow_order[ 'lname' ] );

                                        if ( ($mode == 'reply' && ($answer == 'yesplus' || $answer == 'yes')) || $mode == 'check' ) {
                                            // #########################################################
                                            // #### Display detail screen for yesplus yes and check ####
                                            // #########################################################

                                            $db = tdb_open( "$DOCUMENT_ROOT/bn_dev/tdb/{$config[ "prefix" ]}contact" );
                                            $key = tdb_firstkey( $db );
                                            while ($key != false) {
                                                $data = tdb_fetch( $key, $db );
                                                //echo("{$data['customer']} - $replace_customer<BR>");
                                                if ( $data[ 'customer' ] == $replace_customer ) {
                                                    $replace_emailname = "{$data[ 'firstname' ]} {$data[ 'lastname' ]}";
                                                    $replace_emailsuffix = $data[ 'suffix' ];
                                                    $replace_emailaddress = $data[ 'email' ];
                                                    // here we assign the availability to the replacemtn variable
                                                    //$replace_availability = $data[ 'contact' ];
                                                    if ( $data[ 'captainmf' ] ) {
                                                        $replace_captain = $replace_emailname;
                                                        $replace_captainfirstname = $data[ 'firstname' ];
                                                        $replace_sex = $data[ 'captainmf' ];
                                                    } else {
                                                        $replace_contact = $replace_emailname;
                                                        $replace_contactfirstname = $data[ 'firstname' ];
                                                        //} 
                                                        $replace_sex = $data[ 'contactmf' ];
                                                    }
                                                    // Controleer of het gaat om de Huursloep
                                                    if ( in_array( $myrow_order[ 'shipid' ], $shipidsHuursloep ) && ($answer == 'yesplus' || $answer == 'yes' || $mode == 'check') ) {
                                                        $replace_special = 'HUURSLOEP';
                                                    }
                                                    if ( $myrow_order[ 'shipid' ] == 578 && ($answer == 'yesplus' || $answer == 'yes' || $mode == 'check') ) {
                                                        $replace_special = 'HUURSLOEPADAM';
                                                    }

                                                    $key = false;
                                                }
                                                $key = tdb_nextkey( $db );
                                            }
                                            tdb_close( $db );
                                        }
                                        break;
                                }
                                if ( $mode ) {
                                    $mail = $menu_bar;
                                }
                                $mail .= "<TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>From:</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><A class='main' HREF=\"mailto:{$config[ 'company_email' ]}\">{$config[ 'company_name' ]}</A></TD></TR>";
                                    // maak het gedeelte waar de link naar de beschikbaarheid in staat
                                    //$mail .= "<TR><TH CLASS=main ALIGN=LEFT BGCOLOR=CCCCCC>Avail.:</TD><TD COLSPAN=2 CLASS=main BGCOLOR=CCCCCC><A CLASS=main HREF=\"http://{$replace_availability}\" target=\"_blank\">{$replace_availability}</A></TD></TR>";
                                $from = "{$config[ 'company_name' ]} <{$config[ 'company_email' ]}>";
                                $bcc = $from;
                                //RDC: No owner? no e-mail!
                                if ( !isset( $replace_emailaddress ) ) {
                                    $replace_emailaddress = "<EMAIL>";
                                    $replace_emailname = "<span class='text-danger'>UNKNOWN OWNER</span>";
                                    $replace_emailsuffix = "Do not send!";
                                }
                                if ( $mode == 'reply' ) {
                                    $radioNoChecked = ($answer == 'no') && $myrow_order_detail[ 'copiedRequest' ];
                                        // Reply(-) (i.e. answer=no) AND copied then set sendmail to no
                                    $mail .= "<TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>To:</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><A class='main' HREF=\"mailto:{$myrow_order[ 'email' ]}\">{$myrow_order[ 'fname' ]} {$myrow_order[ 'lname' ]}</A>&nbsp;&nbsp;&nbsp;\n                                            <b>Send Mail?:</b> <input type=\"radio\" name=\"sendmail\" value=\"yes\" " . ($radioNoChecked ? "" : "checked=\"checked\"") . ">Yes <input type=\"radio\" name=\"sendmail\" value=\"no\" " . ($radioNoChecked ? "checked=\"checked\"" : "") . ">No</TD></TR>";
                                    $to = "{$myrow_order[ 'lname' ]} <{$myrow_order[ 'email' ]}>";
                                    if ( $answer == 'yesplus' ) {
                                        //$bcc = "$replace_emailname ($replace_emailsuffix) <$replace_emailaddress>, $bcc";
                                        $to2 = "$replace_emailname ($replace_emailsuffix) <$replace_emailaddress>";
                                    }
                                } else {
                                    $mail .= "<TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>To:</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><A class='main' HREF=\"mailto:$replace_emailaddress\">$replace_emailname ($replace_emailsuffix)</A></TD></TR>";
                                    $to = "$replace_emailname ($replace_emailsuffix) <$replace_emailaddress>";
                                }
                                // dermine the answer state 

                                $tmp = "Mailer: IA";
                                switch (true) {
                                    case $mode == 'check':
                                        $tmp .= ' Schipper' . (isset( $replace_special ) ? " $replace_special" : '');
                                        break;
                                    case $answer == 'yesplus':
                                        $tmp .= '+!' . (isset( $replace_special ) ? " $replace_special" : '');
                                        break;
                                    case $answer == 'yes':
                                        $tmp .= '+' . (isset( $replace_special ) ? " $replace_special" : '');
                                        break;
                                    case $answer == 'no':
                                        $tmp .= '-';
                                        break;
                                    default:
                                        echo "dit gaat helemaal fout!";
                                }

                                echo "Rij uit bn_translation: " . $tmp; // . " translatestring:" . $translatestring . " answer:" . $answer. "<br><br>";
                                //ppr($search)."#".ppr($replace);
                                $bodyText = $translation_form[ $tmp ][ $l_form ];
                                //e($bodyText);//DEBUG
                                $textarea = str_replace( $search, $replace, $bodyText ) . "\r\n";
                                $textarea = nl2br( $textarea );
                                $hidden = "<INPUT TYPE=HIDDEN NAME=mode VALUE=\"" . $mode . "\"><INPUT TYPE=HIDDEN NAME=answer VALUE=\"" . $answer . "\"><INPUT TYPE=HIDDEN NAME=from VALUE=\"" . $from . "\"><INPUT TYPE=HIDDEN NAME=to VALUE=\"" . $to . "\"><INPUT TYPE=HIDDEN NAME=bcc VALUE=\"" . $bcc . "\">";
                                $hidden .= ($answer == 'yesplus' ? "<INPUT TYPE=HIDDEN NAME=to2 VALUE=\"" . $to2 . "\"><INPUT TYPE=HIDDEN NAME=firstname VALUE=\"" . ($replace_captain ? $replace_captainfirstname : $replace_contactfirstname) . "\"><INPUT TYPE=HIDDEN NAME=name VALUE=\"" . $name . "\"><INPUT TYPE=HIDDEN NAME=signature VALUE=\"" . ($user == 'rdc' ? 'Roberto della Chiesa' : 'Jan Huisman') . "\">" : '');
                                $mail .= "<TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>Bcc:</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><A class='main' HREF=\"mailto:{$config[ 'company_email' ]}\">{$config[ 'company_name' ]}</A>" . ($answer == 'yesplus' ? "; <A class='main' HREF=\"mailto:$replace_emailaddress\">$replace_emailname ($replace_emailsuffix)</A>" : '') . "</TD></TR>";
                                $mail .= "<TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>Subject:&nbsp;</TH>\n                    <TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><input type='text' name='subject' value=\"{BOOT} - {PERIOD} - {BNNUMMER}\" class='form-control'></TD></TR>";
                                $mail .= $pixtrans;
                                $mail .= "<TR><TD class='main' COLSPAN=3><textarea name='reply' rows='16' class='form-control'>";
                                $mail .= $textarea;
                                $mail .= "</textarea></TD></TR>";
                            } else {
                                $mail .= "<BR> &nbsp; &nbsp; &nbsp; $hidden<INPUT TYPE=SUBMIT NAME=submit VALUE=\"Go\" CLASS=mainbutton>&nbsp; &nbsp;<INPUT TYPE=RESET NAME=reset VALUE=\"Reset\" CLASS=mainbutton><INPUT TYPE=HIDDEN NAME=user VALUE-$user><INPUT TYPE=HIDDEN NAME=page VALUE=$page><INPUT TYPE=HIDDEN NAME=id VALUE=$id><INPUT TYPE=HIDDEN NAME=mode VALUE=$mode></FORM><BR>";
                            }
                        }
                        if ( $popup_var ) {
                            if ( !$replied ) {
                                if ( isset( $myrow_order_detail[ 'days' ] ) ) {
                                    $popup_var = str_replace( '{DAYS}', $myrow_order_detail[ 'days' ], $popup_var );
                                }
                                $popup_link = " &nbsp; &nbsp; <A class='main' HREF=\"#\" onClick=\"javascript: window.open('bn_mailer_popup.php?$popup_var#$popup_id', 'mailerpopup', config='height=540,width=570,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,directories=no,status=no');\"><U>Alternatives</U></A>";
                            } else {
                                $popup_var = '';
                            }
                        }
                        // Toekennen ordernummer 
                        // replace with nothing, I don't know if this code gets called anymore, too much time to find out
                        $mail = str_replace( '{LINK:YES}', "", $mail );
                        $mail = str_replace( '{LINK:NO}', "", $mail );
                        $mail = str_replace( '{LINK:MAYBE}', "", $mail );
                        $mail = str_replace( '{LINK:INCONTACT}', "", $mail );

                        // if copiedRequest then alternativeboat
                        $boatName = shipid2name( $myrow_order[ 'shipid' ] );
                        // if original not exists (deleted or bootnodig made the request, in which case copiedRequest = 1) 
                        if ( $myrow_order[ 'copiedRequest' ] && ($origRequest = getOrigRequest( $id )) ) { // i really mean a single =
                            // replace labels sameboat, alternativeboat to inform clients about availablity of a boat
                            // inform client other ship is available, requested one is not
                            $available_state = $translation_form[ 'alternativeboat' ][ $myrow_order[ 'language' ] ];
                            $origBoatName = shipid2name( $origRequest[ 'shipid' ] );
                            $available_state = str_replace( array("{BOOT}", "{ALTERNATIEF}"), array($origBoatName, $boatName), $available_state );
                        } else {
                            // current boat
                            $available_state = $translation_form[ 'sameboat' ][ $myrow_order[ 'language' ] ];
                            $available_state = str_replace( "{BOOT}", $boatName, $available_state );
                        }
                        $mail = str_replace( '{AVAILABLE_STATE}', $available_state, $mail );

                        $mail = str_replace( '{BOOT}', $replace_boat, $mail );
                        $mail = str_replace( '{BNNUMMER}', $replace_bnnummer, $mail );
                        
                        // dit blok is speciaal voor de self collect
                        if (bn_isSelfCollect($id)) {
                            $mail = str_replace( '{DEADLINE}', $replace_deadline, $mail );
                            $mail = str_replace( '{TAX}', $replace_tax, $mail );
                            $mail = str_replace( '{TOTAALBEDRAG}', $replace_totaalbedrag, $mail );
                            $mail = str_replace( '{HUURBEDRAG}', $replace_huurbedrag, $mail );
                            $mail = str_replace( '{BORG}', $replace_borg, $mail );
                            //$mail = str_replace( '{TAN}', $replace_tan ?? '', $mail );
                            $mail = str_replace( '{PAYMENTLINK}', $replace_paymentlink, $mail );
                            $cancelLink = cancelRequestURL( $myrow_order[ 'ID' ] );
                            $mail = str_replace( '{CANCEL-LINK}', $cancelLink, $mail );
                        }

                        $mail = str_replace( '{URL}', GetURL( $myrow_order[ 'shipid' ] ), $mail );
                        // ask owner if prices are still up to date
                        $boatPrices = boatPropsTable( $replace_customer );
                        $mail = str_replace( '{BOATPRICES}', $boatPrices, $mail ); //3 juli 2019
                        $mail = str_replace( '{VOORNAAM CONTACTPERSOON/SCHIPPER}', ($replace_captain ? $replace_captainfirstname : $replace_contactfirstname ), $mail );
                        $mail = str_replace( '{JE/JULLIE}', ($replace_sex == 'MF' ? 'jullie' : 'je' ), $mail );

                        $mail = str_replace( '{OP ##-##-####/GEDURENDE ##-##/##-##-####}', formatDateOrPeriod( $myrow_order[ 'startt' ], $myrow_order[ 'endt' ] ), $mail );
                        $mail = str_replace( '{OP EEN ANDERE DAG/GEDURENDE EEN ANDERE PERIODE}', ($replace_day ? $translation_form[ 'op een andere dag' ][ $myrow_order[ 'language' ] ] : $translation_form[ 'gedurende een andere periode' ][ $myrow_order[ 'language' ] ] ), $mail );
                        $mail = str_replace( '{OP DE OORSPRONKELIJKE DATUM/GEDURENDE DE OORSPRONKELIJKE PERIODE}', ($replace_day ? $translation_form[ 'op de oorspronkelijke datum' ][ $myrow_order[ 'language' ] ] : $translation_form[ 'gedurende de oorspronkelijke periode' ][ $myrow_order[ 'language' ] ] ), $mail );
                        $mail = str_replace( '{DAG/NACHT}', ($replace_day ? $translation_form[ 'day' ][ $myrow_order[ 'language' ] ] : $translation_form[ 'night' ][ $myrow_order[ 'language' ] ] ), $mail );

                        $mail = str_replace( '{NAAM CONTACTPERSOON/SCHIPPER}', ($replace_captain ? $replace_captain : $replace_contact ), $mail );
                        $mail = str_replace( '{CONTACTPERSOON/SCHIPPER}', ($replace_captain ? ($replace_sex == 'MF' ? $translation_form[ 'schippers' ][ $myrow_order[ 'language' ] ] : $translation_form[ 'schipper' ][ $myrow_order[ 'language' ] ]) : $translation_form[ 'contactpersoon' ][ $myrow_order[ 'language' ] ] ), $mail );
                        $mail = str_replace( '{HIJ/ZIJ ZAL/ZULLEN}', ($replace_sex == 'MF' ? $translation_form[ 'Zij zullen' ] : ($replace_sex == 'M' ? $translation_form[ 'Hij zal' ][ $myrow_order[ 'language' ] ] : $translation_form[ 'Zij zal' ][ $myrow_order[ 'language' ] ]) ), $mail );

                        $mail = str_replace( '{PERIOD}', $replace_period, $mail );
                        $mail = str_replace( '. De De ', '. De ', $mail );
                        $mail = str_replace( '. De Stockpaerdt', '. Het Stockpaerdt', $mail );
                        $mail = str_replace( '. Wapen fan Frysln', '. Het Wapen fan Frysln', $mail );
                        $mail = str_replace( 'van de De ', 'van De ', $mail );
                        $mail = str_replace( 'van de Stockpaerdt', ' van het Stockpaerdt', $mail );
                        $mail = str_replace( 'van de Wapen fan Frysln', 'van het Wapen van Frysln', $mail );

                        $mail = str_replace( '{JAN SINNEMA/JOHANNES JENSMA}', ($user == 'rdc' ? 'Roberto della Chiesa' : 'Jan Huisman' ), $mail );
                        $mail = str_replace( '{POPUPLINK}', $popup_link, $mail );
                        $mail = str_replace( '€', '&euro;', $mail );
                        $option = $myrow_order_detail[ 'yesplus' ];
                        if ( is_null( $option ) ) {
                            $option = 1;
                        }
                        $lang = $myrow_order_detail[ 'language' ];
                        if ( $lang == 'en' ) {
                            $option = addOrdinalNumberSuffix( $option );
                        }
                        $mail = str_replace( '{OPTION}', $option, $mail ); // client waiting queue
                        $mail = str_replace( '€', '&euro;', $mail );
                        echo $mail; // #### here the built emailbody is shown on the screen !!!!
                        // Coen deleted this next block oops
                        if ( isset( $mode ) ) {
                            if ( $mode == "reply" && $answer == "yesplus" ) {
                                // make subject and put into var, need it further on
                                $subjectShown = "{BOOT} - {PERIOD} - {BNNUMMER}";
                                $subjectShown = str_replace( '{BOOT}', $boatName, $subjectShown );
                                $subjectShown = str_replace( '{PERIOD}', $replace_period, $subjectShown );
                                $subjectShown = str_replace( '{BNNUMMER}', $replace_bnnummer, $subjectShown );
                                $transKey = 'Mailer: IA+! Schipper' . $specialcompany;
                                $reply_captain = $translation_form[ $transKey ][ 'nl' ];
                                $str_search = array(
                                    '{URL}'
                                    , '{VOORNAAM CONTACTPERSOON/SCHIPPER}'
                                    , '{NAAM}'
                                    , '{%EMAIL_FOOTER%}'
                                );
                                $str_replace = array(
                                    GetURL( $myrow_order[ 'shipid' ] )
                                    , $skipper[ 'firstname' ]
                                    , $name
                                    , getEmailFooterOwner()
                                );
                                $reply_captain = stripslashes( str_replace( $str_search, $str_replace, $reply_captain ) );
                                $reply_captain = str_replace( "\r", "<br>", $reply_captain );
                                //echo($reply_captain);die;
                                ?>
                                <tr><td><br></td></tr>
                                <tr><td colspan="2">Rij uit bn_translation: <?= $transKey ?></td></tr>
                                <TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>From:</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><A class='main' HREF=\"mailto:<?= $config[ 'company_email' ] ?>\"><?= $config[ 'company_name' ] ?></A></TD></TR>
                                <?php
                                $toSkipper = $replace_emailaddress;
                                if ( isset( $skipper[ 'name' ] ) && !empty( $skipper[ 'name' ] ) ) {
                                    $toSkipper = $skipper[ 'name' ] . " <" . $toSkipper . ">";
                                }
                                ?>
                                <TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>To:</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><A class='main' HREF=\"mailto:<?= $toSkipper ?>\"><?= $replace_emailname . " (" . $replace_emailsuffix . ")" ?></A></td>
                                <TR><TH class='main' ALIGN=LEFT BGCOLOR=CCCCCC>Subject:&nbsp;</TH><TD class='main' COLSPAN=2 BGCOLOR=CCCCCC><input type='text' name='subjectSkipper' value='<?= $subjectShown ?>' class='form-control'></td>
                                </TR>
                                <tr>
                                    <td colspan='3'>
                                        <!-- $textarea contains the content of the email to the skipper -->
                                        <textarea name='reply_captain' ROWS=10 COLS=80><?= $reply_captain . "<br>" . $content ?></textarea>
                                    </td>
                                </tr>
                            <?php } ?>
                            <TR>
                                <TD class='main' COLSPAN=3 ALIGN=CENTER><BR><?= $hidden ?>
                                    <INPUT TYPE=SUBMIT NAME=submit VALUE="Go" class='btn btn-primary' style="font-size: 300%;width: 45%;">&nbsp; &nbsp;&nbsp; &nbsp;
                                    <INPUT TYPE=RESET NAME=reset VALUE="Reset" class='btn btn-secondary' style="font-size: 300%;width: 45%;">
                                    <INPUT TYPE=HIDDEN NAME=user VALUE="<?= $user ?>">
                                    <INPUT TYPE=HIDDEN NAME=page VALUE="<?= $page ?>">
                                    <INPUT TYPE=HIDDEN NAME=id VALUE="<?= $id ?>">
                                    <INPUT TYPE=HIDDEN NAME=mode VALUE="<?= $mode ?>">
                                </TD>
                            </TR>
                            <?php
                        }
                        // COen end oops
                    }
                    ?>
                    <?php if ( isset( $id ) ) { ?>
                    </TABLE>
                    <?php } ?>
                    <?php
                    if ( !isset( $id ) ) {
                        ?>
                    </form>
                    <?php
                }
                ?>
            </div><div id = "right">
                <?php
                if ( $mode <> "reply" && $mode <> "check" && isset( $myrow_order_detail[ 'shipid' ] ) && isset( $id_to_avail[ $myrow_order_detail[ 'shipid' ] ] ) ) {
                    if ( in_array( $myrow_order_detail[ 'shipid' ], huursloepShipids() ) ) {
                        $link = bn_Request2LetsBookUrl( $myrow_order_detail );
                    } else {
                        $link = $id_to_avail[ $myrow_order_detail[ 'shipid' ] ];
                    }
                    echo "Available: <a href=\"{}\">link</a><br><iframe width='1000' height='1000' src=\"{}\">Link to availability!!</iframe>";
                }
                ?>
            </div> <!-- outer table -->
            <footer class="text-center mt-5">
            <p class="text-muted small">
                <a href="http://www.enformation.nl" target="_blank" class="text-decoration-none">Copyright 2001-2014 Enformation</a> / 
                <a class="text-decoration-none" href="mailto:<EMAIL>"><EMAIL></a>
            </p>
        </footer>
        </BODY>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</HTML>