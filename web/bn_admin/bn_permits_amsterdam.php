<?php
// === Include or define the functions above ===
// For example, if they are in 'extractor_functions.php':
require_once 'bn_include_scraping.php';

// === Main Script ===
// The URL to scrape
$url = "https://www.amsterdam.nl/ondernemen/passagiers-beroepsvaart/beroepsvaart/register-passagiersvaart-amsterdam/";

// Call the extraction function
$data_result = extractAmsterdamShippingData($url);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Register Passagiersvaart Amsterdam</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        .container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h2 { color: #2c3e50; }
        p.error { color: #c0392b; background-color: #fdd; border: 1px solid #c0392b; padding: 10px; border-radius: 4px;}
        table { border-collapse: collapse; width: auto; margin-top: 20px; font-size: 0.9em; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        caption { caption-side: bottom; text-align: right; font-size: 0.8em; color: #777; margin-top: 5px; }
        th, td { border: 1px solid #ccc; padding: 8px 12px; text-align: left; }
        th { background-color: #3498db; color: white; }
        th button { background: none; border: none; font-weight: bold; font-family: inherit; font-size: inherit; cursor: pointer; padding: 0; margin: 0; width: 100%; text-align: inherit; color: white;}
        th button:hover { text-decoration: underline; }
        .sort-asc::after { content: ' ▲'; font-size: 0.8em; }
        .sort-desc::after { content: ' ▼'; font-size: 0.8em; }
        tr:nth-child(even) { background-color: #ecf0f1; }
    </style>
</head>
<body>
<div class="container">
    <?php if ($data_result['success']): ?>
        <h2><?php echo htmlspecialchars($data_result['title']); ?></h2>
        <?php if (!empty($data_result['rows'])): ?>
            <table id="extractedTable">
                <?php if (!empty($data_result['caption'])): ?>
                    <caption><?php echo htmlspecialchars($data_result['caption']); ?></caption>
                <?php endif; ?>
                <thead>
                    <tr>
                        <th><button onclick="sortTable(0, this)"><?php echo htmlspecialchars($data_result['headers'][0]); ?></button></th>
                        <th><button onclick="sortTable(1, this)"><?php echo htmlspecialchars($data_result['headers'][1]); ?></button></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data_result['rows'] as $data_row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars(isset($data_row[0]) ? $data_row[0] : ''); ?></td>
                        <td><?php echo htmlspecialchars(isset($data_row[1]) ? $data_row[1] : ''); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>Data was extracted successfully, but no rows were found in the table.</p>
        <?php endif; ?>
    <?php else: ?>
        <h2>Error Extracting Data</h2>
        <p class="error"><?php echo htmlspecialchars($data_result['error']); ?></p>
    <?php endif; ?>
</div>

<script>
    let sortDirections = [undefined, undefined];

    function sortTable(columnIndex, button) {
        const table = document.getElementById('extractedTable');
        if (!table) return;
        const tbody = table.tBodies[0];
        if (!tbody) return;
        const rows = Array.from(tbody.rows);
        const headerButtons = table.tHead.querySelectorAll('button');

        let currentDirection = sortDirections[columnIndex];
        if (currentDirection === undefined || currentDirection === 'desc') {
            currentDirection = 'asc';
        } else {
            currentDirection = 'desc';
        }
        sortDirections[columnIndex] = currentDirection;

        for (let i = 0; i < headerButtons.length; i++) {
            headerButtons[i].classList.remove('sort-asc', 'sort-desc');
            if (i !== columnIndex) {
                sortDirections[i] = undefined;
            }
        }
        button.classList.add(currentDirection === 'asc' ? 'sort-asc' : 'sort-desc');
        const directionMultiplier = currentDirection === 'asc' ? 1 : -1;

        rows.sort((rowA, rowB) => {
            const cellA_node = rowA.cells[columnIndex];
            const cellB_node = rowB.cells[columnIndex];
            const cellA = cellA_node ? cellA_node.textContent.trim().toLowerCase() : '';
            const cellB = cellB_node ? cellB_node.textContent.trim().toLowerCase() : '';
            
            if (cellA < cellB) return -1 * directionMultiplier;
            if (cellA > cellB) return 1 * directionMultiplier;
            return 0;
        });
        rows.forEach(row => tbody.appendChild(row));
    }
</script>
</body>
</html>