<?php
// Helper function to fetch HTML content
function get_html_content_from_url($url) {
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return ['success' => false, 'error' => 'Invalid URL provided.'];
    }

    $context_options = [
        'http' => [
            'header' => "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3\r\n",
            'timeout' => 10, // Timeout in seconds
            'follow_location' => 1, // Follow redirects
            'max_redirects' => 5
        ],
        // SSL context options to potentially help with HTTPS sites, use with caution
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false,
        ]
    ];
    $context = stream_context_create($context_options);
    $content = @file_get_contents($url, false, $context);

    if ($content === FALSE) {
        // Try with cURL if file_get_contents fails
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For HTTPS, consider security implications
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // For HTTPS, consider security implications
            $content = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($content === false || $http_code >= 400) {
                return ['success' => false, 'error' => "cURL Error: Failed to fetch URL. HTTP Code: " . $http_code . " " . curl_error($ch)];
            }
            return ['success' => true, 'content' => $content];
        } else {
            return ['success' => false, 'error' => 'file_get_contents failed and cURL extension is not available.'];
        }
    }
    return ['success' => true, 'content' => $content];
}

// Independent function to extract data
function extractAmsterdamShippingData($url) {
    $table_title_text = "Register Passagiersvaart Amsterdam";

    $fetch_result = get_html_content_from_url($url);
    if (!$fetch_result['success']) {
        return ['success' => false, 'error' => $fetch_result['error']];
    }
    $html_content = $fetch_result['content'];

    if (empty($html_content)) {
        return ['success' => false, 'error' => 'Fetched HTML content is empty.'];
    }

    $doc = new DOMDocument();
    // Suppress errors from malformed HTML and ensure UTF-8 processing
    libxml_use_internal_errors(true);
    if (!$doc->loadHTML('<?xml encoding="utf-8" ?>' . $html_content)) {
        $errors = array_map(function($error) { return $error->message; }, libxml_get_errors());
        libxml_clear_errors();
        return ['success' => false, 'error' => 'Failed to parse HTML. Errors: ' . implode("; ", $errors)];
    }
    libxml_clear_errors();


    $xpath = new DOMXPath($doc);

    // Primary XPath query based on the h2 title and subsequent table structure
    $query = "//h2[normalize-space(.)='" . $table_title_text . "']/following-sibling::div[1]//table[1]";
    $tables = $xpath->query($query);

    // Fallback queries if the primary one fails
    if (!$tables || $tables->length == 0) {
        $query_fallback_caption = "//table[caption[contains(normalize-space(.), '" . $table_title_text . "')]]";
        $tables = $xpath->query($query_fallback_caption);
    }
    if (!$tables || $tables->length == 0) {
        $query_fallback_generic = "//*[self::h1 or self::h2 or self::h3 or self::p or self::strong][contains(normalize-space(.), '" . $table_title_text . "')]/following::table[1]";
        $tables = $xpath->query($query_fallback_generic);
    }

    if (!$tables || $tables->length == 0) {
        return ['success' => false, 'error' => "Could not find the table titled '" . htmlspecialchars($table_title_text) . "'."];
    }

    $table_node = $tables->item(0);
    $extracted_rows_data = [];
    $extracted_headers = [];
    $extracted_caption = '';

    // Extract Caption
    $caption_node = $xpath->query(".//caption", $table_node);
    if ($caption_node && $caption_node->length > 0) {
        $extracted_caption = trim($caption_node->item(0)->nodeValue);
    }

    // Extract Headers (from <thead> if present, otherwise first <tr> with <th> or <td>)
    $header_nodes = $xpath->query(".//thead/tr/th", $table_node);
    if ($header_nodes->length == 0) { $header_nodes = $xpath->query(".//tr[1]/th", $table_node); }
    if ($header_nodes->length == 0) { $header_nodes = $xpath->query(".//tr[1]/td", $table_node); }

    foreach ($header_nodes as $th) {
        $extracted_headers[] = trim($th->nodeValue);
    }

    // Ensure we have exactly two headers, or default them if necessary for consistency
    if (count($extracted_headers) >= 2) {
        $extracted_headers = array_slice($extracted_headers, 0, 2);
    } elseif (count($extracted_headers) == 1) {
        $extracted_headers[] = "Naam Vaartuig (auto)"; // Adjusted default
    } else {
        $extracted_headers = ["Vergunninghouder (auto)", "Naam Vaartuig (auto)"]; // Adjusted defaults
    }

    // Extract table data rows
    $data_row_nodes = $xpath->query(".//tbody/tr", $table_node);
    if ($data_row_nodes->length == 0) { // Fallback for tables without explicit <tbody> or if rows are direct children
        $all_tr_nodes = $xpath->query(".//tr", $table_node);
        $temp_rows = [];
        $first_row_is_header = false;
        if ($all_tr_nodes->length > 0 && $header_nodes->length > 0) {
            $first_tr_first_cell_node = $xpath->query("./th[1] | ./td[1]", $all_tr_nodes->item(0));
            if ($first_tr_first_cell_node->length > 0) {
                $first_tr_first_cell_content = trim($first_tr_first_cell_node->item(0)->nodeValue);
                if ($first_tr_first_cell_content === $extracted_headers[0]) {
                    $first_row_is_header = true;
                }
            }
        }
        for ($i = ($first_row_is_header ? 1 : 0); $i < $all_tr_nodes->length; $i++) {
            $temp_rows[] = $all_tr_nodes->item($i);
        }
        $data_row_nodes = $temp_rows; // This will be an array of DOMNode objects
    }

    foreach ($data_row_nodes as $row_node) {
        if (!$row_node instanceof DOMNode) continue; // Ensure it's a node
        $cells = $xpath->query(".//td", $row_node);
        $row_item = [];
        if ($cells->length >= 1) {
            $row_item[] = trim($cells->item(0)->nodeValue);
            if ($cells->length >= 2) {
                $row_item[] = trim($cells->item(1)->nodeValue);
            } else {
                $row_item[] = ''; // Empty string if second cell is missing
            }
            $extracted_rows_data[] = $row_item;
        }
    }

    if (empty($extracted_rows_data) && count($extracted_headers) === 2 && $extracted_headers[0] === "Vergunninghouder (auto)") {
         // This check is a bit heuristic, maybe the table was found but data extraction was poor.
        // Or it truly is an empty table with auto-generated headers.
        // return ['success' => false, 'error' => 'Table found, but no data rows could be extracted. Headers might be default values.'];
    }


    return [
        'success' => true,
        'headers' => $extracted_headers,
        'rows' => $extracted_rows_data,
        'caption' => $extracted_caption,
        'title' => $table_title_text
    ];
}

?>